import {
  getEmployersWithholdingByEmployer_id,
  getUsersCurrent,
  HttpError,
  patchUsersByUser_id,
  postEmployersAdd,
  postEmployersVerifications,
  UserResponse,
  WithholdingResponse,
} from "../api";
import { ConfigFunction } from "../config";
import { Credentials, OAuthCreds } from "../types";
import AutoCache from "../util/AutoCache";
import {
  getClaimantToken,
  getLeaveAdminToken,
  registerClaimant as registerMmgClaimant,
  registerLeaveAdmin as registerMmgLeaveAdmin,
  RegistrationOptions,
} from "../util/myMassGov";
import { generateFakeContactInformation } from "../util/pii";
import TestMailVerificationFetcher from "./TestMailVerificationFetcher";

export default class AuthenticationManager {
  apiBaseUrl?: string;
  verificationFetcher?: TestMailVerificationFetcher;

  /**
   * Creates a new instance based on configuration.
   *
   * @param config
   */
  static create(config: ConfigFunction) {
    return new AuthenticationManager(
      config("API_BASEURL"),
      new TestMailVerificationFetcher(
        config("TESTMAIL_APIKEY"),
        config("TESTMAIL_NAMESPACE")
      )
    );
  }

  constructor(
    apiBaseUrl?: string,
    verificationFetcher?: TestMailVerificationFetcher
  ) {
    this.apiBaseUrl = apiBaseUrl;
    this.verificationFetcher = verificationFetcher;
  }

  async registerClaimant(
    credentials: Credentials,
    options: RegistrationOptions = {}
  ) {
    await registerMmgClaimant(credentials, options);
    const token = await getClaimantToken(credentials);
    return this.consentToDataSharing(token);
  }

  async registerLeaveAdmin(
    credentials: Credentials,
    fein: string,
    options: RegistrationOptions = {}
  ) {
    try {
      await registerMmgLeaveAdmin(credentials, options);
      const token = await getLeaveAdminToken(credentials);
      await this.consentToDataSharing(token);
      await this.addEmployer(fein, token);
    } catch (e) {
      // A 409 means that the LA already has an associated employer, so the
      // account is fully formed.
      if (e.data?.status_code !== 409) {
        throw e;
      }
    }
  }

  async getAPIBearerToken(credentials: OAuthCreds): Promise<string> {
    const { clientId, clientSecret } = credentials;
    const credentialPair = `${clientId}:${clientSecret}`;
    const encodedCredentials = Buffer.from(credentialPair).toString("base64");

    const bodyParameters: Record<string, string> = {
      grant_type: "client_credentials",
      scope: `${clientId}/.default`,
    };

    const opts = {
      method: "POST",
      headers: {
        Authorization: `Basic ${encodedCredentials}`,
        "User-Agent": "PFML Integration Testing",
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: Object.entries(bodyParameters)
        .filter(([, value]) => value)
        .map((entry) => entry.join("="))
        .join("&"),
    };

    const url = `${this.apiBaseUrl}/oauth2/token2`;
    const response = await fetch(url, opts);

    if (response.ok) {
      const parsedResponse: TokenSuccessResponse = await response.json();
      const cache = new AutoCache({
        fetchValue: async () => parsedResponse.access_token,
        getExpirationDate: () => {
          const expirationDate = new Date();
          expirationDate.setSeconds(
            expirationDate.getSeconds() + parsedResponse.expires_in
          );
          return expirationDate;
        },
      });
      cache.set("bearerToken", parsedResponse.access_token);
      return parsedResponse.access_token;
    }

    const parsedResponse: TokenErrorResponse = await response.json();
    // error_description is more detailed than error. Provided by MMG OAuth.
    const error = parsedResponse.error_description || parsedResponse.error;
    throw new Error(`Failed to get API bearer token: ${error}`);
  }

  private async addEmployer(fein: string, token: string) {
    const apiOptions = this.generateBearerApiOptions(token);
    await postEmployersAdd({ employer_fein: fein }, apiOptions);
  }

  private generateBearerApiOptions(token: string) {
    if (!this.apiBaseUrl) {
      throw new Error(
        "Unable to generate API options: no API base URL was given."
      );
    }

    return {
      baseUrl: this.apiBaseUrl,
      headers: {
        Authorization: `Bearer ${token}`,
        "User-Agent": "PFML Business Simulation Bot",
      },
    };
  }

  async verifyLeaveAdmin(
    credentials: Credentials,
    withholdings: Record<string, number>
  ) {
    if (!this.apiBaseUrl) {
      throw new Error(
        "No api base URL was given. Unable to consent to data sharing."
      );
    }
    const token = await getLeaveAdminToken(credentials);
    const apiOptions = this.generateBearerApiOptions(token);
    const user = (await getUsersCurrent(apiOptions)) as unknown as {
      data: { data: UserResponse };
    };
    const { user_leave_administrators } = user.data.data;
    if (!user_leave_administrators || user_leave_administrators.length === 0) {
      throw new Error("No leave administrators found");
    }
    const employer_id = user_leave_administrators[0].employer_id;
    if (!employer_id) {
      throw new Error("No employer ID found");
    }
    const employersWithholdingByEmployer_id =
      (await getEmployersWithholdingByEmployer_id(
        { employer_id },
        apiOptions
      )) as unknown as {
        data: { data: WithholdingResponse };
      };
    const withholding_quarter = String(
      employersWithholdingByEmployer_id.data.data.filing_period
    );
    try {
      await postEmployersVerifications(
        {
          employer_id: employer_id,
          withholding_amount: withholdings[withholding_quarter],
          withholding_quarter,
        },
        apiOptions
      );
    } catch (e) {
      // Ignore "Already verified" errors. We consider that not a problem
      // for the authenticator. The goal was to verify them, and they already are.
      if (e instanceof HttpError && e.status === 409) {
        return;
      }
      throw e;
    }
  }

  private async consentToDataSharing(token: string) {
    if (!this.apiBaseUrl) {
      throw new Error(
        "No api base URL was given. Unable to consent to data sharing."
      );
    }
    const apiOptions = this.generateBearerApiOptions(token);
    const user = (await getUsersCurrent(apiOptions)) as unknown as {
      data: { data: { user_id: string } };
    };
    if (!user.data.data.user_id) {
      throw new Error("No user ID found");
    }

    // Approve data sharing for this user.
    await patchUsersByUser_id(
      {
        user_id: user.data.data.user_id,
      },
      {
        consented_to_data_sharing: true,
      },
      apiOptions
    );
  }

  async setLeaveAdminContactInformation(credentials: Credentials) {
    if (!this.apiBaseUrl) {
      throw new Error(
        "No api base URL was given. Unable to set leave admin contact information."
      );
    }
    const token = await getLeaveAdminToken(credentials);
    const apiOptions = this.generateBearerApiOptions(token);
    const user = (await getUsersCurrent(apiOptions)) as unknown as {
      data: { data: { user_id: string } };
    };
    if (!user.data.data.user_id) {
      throw new Error("No user ID found");
    }
    const { firstName, lastName, phoneNumber } =
      generateFakeContactInformation();
    // Approve data sharing for this user.
    await patchUsersByUser_id(
      {
        user_id: user.data.data.user_id,
      },
      {
        first_name: firstName,
        last_name: lastName,
        phone: { phone_number: phoneNumber, phone_type: "Cell" },
      },
      apiOptions
    );
  }
}

interface TokenErrorResponse {
  error: string;
  // @TODO PFMLPB-17326: Make error_description required.
  error_description?: string;
}

interface TokenSuccessResponse {
  access_token: string;
  expires_in: number;
  token_type: string;
}
