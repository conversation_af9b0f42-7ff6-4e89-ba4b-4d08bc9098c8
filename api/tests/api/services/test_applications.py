import uuid
from datetime import date, timedelta
from unittest.mock import Mock, patch

import pytest
from freezegun import freeze_time
from werkzeug.datastructures import FileStorage

import massgov.pfml.api.services.applications as service
from massgov.pfml.api.models.applications.common import MmgIdvStatus as MmgIdvStatusEnum
from massgov.pfml.api.models.applications.common import OrganizationUnitSelection
from massgov.pfml.api.models.applications.requests import ApplicationRequestBody
from massgov.pfml.api.models.documents.common import DocumentType
from massgov.pfml.api.services.applications import link_address_with_employee
from massgov.pfml.api.services.claims import get_previous_absence_period_from_claim
from massgov.pfml.db.lookup_data.absences import AbsencePeriodType
from massgov.pfml.db.lookup_data.applications import AmountFrequency, MmgIdvStatus
from massgov.pfml.db.lookup_data.applications import Race as PfmlRace
from massgov.pfml.db.lookup_data.employees import Gender as PfmlGender
from massgov.pfml.db.lookup_data.employees import LeaveRequestDecision
from massgov.pfml.db.lookup_data.language import Language
from massgov.pfml.db.models.applications import Application, EmployerBenefit, PreviousLeave
from massgov.pfml.db.models.employees import (
    Address,
    BenefitYear,
    EmployeeAddress,
    ExperianAddressPair,
)
from massgov.pfml.db.models.factories import (
    AbsencePeriodFactory,
    ApplicationFactory,
    BenefitYearFactory,
    ClaimFactory,
    ContinuousLeavePeriodFactory,
    EmployeeFactory,
    EmployerBenefitFactory,
    EmployerFactory,
    IntermittentLeavePeriodFactory,
    OrganizationUnitFactory,
    OtherIncomeFactory,
    ReducedScheduleLeavePeriodFactory,
)
from massgov.pfml.my_mass_gov.client import get_client as get_mmg_client


@pytest.fixture
@freeze_time("2021-06-14")
def application_split():
    start_end_dates = service.StartEndDates(
        start_date=date.today() - timedelta(days=20), end_date=date.today() - timedelta(days=10)
    )

    crossed_benefit_year = BenefitYear(end_date=date.today())
    return service.ApplicationSplit(
        crossed_benefit_year=crossed_benefit_year,
        application_dates_in_benefit_year=start_end_dates,
        application_dates_outside_benefit_year=start_end_dates,
        application_outside_benefit_year_submittable_on=timedelta(days=20),
    )


@pytest.fixture
def application_spanning_benefit_years(user):
    by_start_date = date(2022, 6, 15)
    by_end_date = by_start_date + timedelta(weeks=52)
    leave_start_date = date(2023, 6, 1)
    leave_end_date = date(2023, 6, 30)

    employee = EmployeeFactory.create()
    BenefitYearFactory.create(start_date=by_start_date, end_date=by_end_date, employee=employee)

    application = ApplicationFactory.create(
        user=user,
        tax_identifier=employee.tax_identifier,
    )
    application.continuous_leave_periods = [
        ContinuousLeavePeriodFactory.create(start_date=leave_start_date, end_date=leave_end_date)
    ]

    return application


def app_split_on_by_date(application, test_db_session):
    split = service.get_application_split(application, test_db_session)
    (app_before_split, app_after_split) = service.split_application_by_date(
        test_db_session, application, split
    )

    return app_before_split, app_after_split


@pytest.mark.parametrize(
    "idv_status_update, application_status_id, expected_mmg_idv_status_id",
    [
        pytest.param(
            MmgIdvStatusEnum.unverified,
            MmgIdvStatus.VERIFIED.id,
            MmgIdvStatus.UNVERIFIED.id,
            id="Verified => Unverified status updates the application",
        ),
        pytest.param(
            MmgIdvStatusEnum.unverified,
            MmgIdvStatus.UNVERIFIED.id,
            MmgIdvStatus.UNVERIFIED.id,
            id="Unverified => Unverified does not change the application",
        ),
        pytest.param(
            MmgIdvStatusEnum.verified,
            MmgIdvStatus.UNVERIFIED.id,
            MmgIdvStatus.UNVERIFIED.id,
            id="Verified => Unverified status does not change the application",
        ),
        pytest.param(
            MmgIdvStatusEnum.verified,
            None,
            None,
            id="Applications outside the IDV pilot cannot be updated to Verified",
        ),
        pytest.param(
            MmgIdvStatusEnum.unverified,
            None,
            None,
            id="Applications outside the IDV pilot cannot be updated to Unverified",
        ),
    ],
)
def test_update_from_application_idv_status(
    test_db_session,
    application,
    idv_status_update,
    application_status_id,
    expected_mmg_idv_status_id,
):

    application.mmg_idv_status_id = application_status_id
    assert application.mmg_idv_status_id is application_status_id

    updated_application = service.update_from_application_idv_status(
        test_db_session,
        idv_status_update,
        application,
    )

    assert updated_application.mmg_idv_status_id == expected_mmg_idv_status_id


@pytest.mark.parametrize(
    "application_status_id, expected_mmg_idv_status_id",
    [
        pytest.param(
            None,
            MmgIdvStatus.VERIFIED.id,
            id="Application updates with MMG IDV status",
        ),
        pytest.param(
            MmgIdvStatus.UNVERIFIED.id,
            MmgIdvStatus.UNVERIFIED.id,
            id="Application does not update when there is an existing MMG IDV status",
        ),
    ],
)
def test_update_from_user_idv_status(
    test_db_session, application, app, application_status_id, expected_mmg_idv_status_id
):
    with app.app.app_context():
        with patch(
            "massgov.pfml.api.app.get_features_config",
            return_value=Mock(universal_profile=Mock(enable_mmg_idv=True)),
        ):
            application.mmg_idv_status_id = application_status_id
            assert application.mmg_idv_status_id is application_status_id
            client = get_mmg_client()
            token = "1234"

            updated_application = service.update_from_user_idv_status(
                test_db_session,
                client,
                token,
                application,
            )

            assert updated_application.mmg_idv_status_id == expected_mmg_idv_status_id


def test_update_from_user_profile(test_db_session, application, app):
    with app.app.app_context():
        assert application.gender_id is None
        assert application.race_id is None
        client = get_mmg_client()
        token = "1234"
        fields_to_use_from_user_profile = {"gender", "raceEthnicity"}

        updated_application = service.update_from_user_profile(
            test_db_session,
            client,
            token,
            application,
            fields_to_use_from_user_profile,
        )

        assert updated_application.gender_id == PfmlGender.NOT_LISTED.gender_id
        assert updated_application.race_id == PfmlRace.ASIAN_ASIAN_AMERICAN.id


def test_update_from_user_profile_wth_ff(test_db_session, application, app):
    with app.app.app_context():
        with patch(
            "massgov.pfml.api.app.get_features_config",
            return_value=Mock(universal_profile=Mock(enable_universal_profile_idv=True)),
        ):
            # overwrite_existing is False in update_application_from_mmg.
            # Nullify the values we want to test, so that they update with values from the mock response
            application.first_name = None
            application.last_name = None
            application.date_of_birth = None
            assert application.first_name is None
            assert application.last_name is None
            assert application.date_of_birth is None
            client = get_mmg_client()
            token = "1234"
            fields_to_use_from_user_profile = {"firstname", "lastname", "dateOfBirth"}

            updated_application = service.update_from_user_profile(
                test_db_session,
                client,
                token,
                application,
                fields_to_use_from_user_profile,
            )

            assert updated_application.first_name == "Ravenjack"
            assert updated_application.last_name == "Sondale"
            assert updated_application.date_of_birth == date(1980, 4, 30)


def test_update_from_user_profile_wth_ff_and_all_fields(test_db_session, application, app):
    with app.app.app_context():
        with patch(
            "massgov.pfml.api.app.get_features_config",
            return_value=Mock(universal_profile=Mock(enable_universal_profile_idv=True)),
        ):
            # overwrite_existing is False in update_application_from_mmg.
            # Nullify the values we want to test, so that they update with values from the mock response
            application.first_name = None
            application.last_name = None
            application.date_of_birth = None
            assert application.gender_id is None
            assert application.race_id is None
            assert application.first_name is None
            assert application.last_name is None
            assert application.date_of_birth is None
            client = get_mmg_client()
            token = "1234"
            fields_to_use_from_user_profile = {
                "gender",
                "raceEthnicity",
                "firstname",
                "lastname",
                "dateOfBirth",
            }

            updated_application = service.update_from_user_profile(
                test_db_session,
                client,
                token,
                application,
                fields_to_use_from_user_profile,
            )

            assert updated_application.first_name == "Ravenjack"
            assert updated_application.last_name == "Sondale"
            assert updated_application.date_of_birth == date(1980, 4, 30)
            assert updated_application.gender_id == PfmlGender.NOT_LISTED.gender_id
            assert updated_application.race_id == PfmlRace.ASIAN_ASIAN_AMERICAN.id


def test_link_address_with_employee(test_db_session, application_spanning_benefit_years):
    # Create and add a new address
    addresses = []
    for street in ["123 Main St", "456 Elm St"]:
        address = Address(
            address_id=uuid.uuid4(),
            address_line_one=street,
            city="Austin",
            geo_state_id=1,
            zip_code="78701",
            country_id=232,
        )
        test_db_session.add(address)
        test_db_session.commit()

        addresses.append(address.address_id)

    application = test_db_session.query(Application).first()

    # Assert no employee_address record exists initially
    initial_count = test_db_session.query(EmployeeAddress).count()
    assert initial_count == 0

    # Call the function to link the address with an employee
    link_address_with_employee(
        test_db_session, addresses[0], addresses[1], application.tax_identifier_id
    )

    # Query again to check if a new record has been added
    final_count = test_db_session.query(EmployeeAddress).count()
    # Ensure exactly one record was added
    assert final_count == initial_count + 2


def test_selection_to_default_org_unit(test_db_session, application, monkeypatch):

    employer_to_map = EmployerFactory.create()
    org_unit = OrganizationUnitFactory(employer_id=employer_to_map.employer_id)
    org_unit_default = OrganizationUnitFactory(
        employer_id=employer_to_map.employer_id, is_default_organization_unit=True
    )

    application_body = ApplicationRequestBody(organization_unit_id=org_unit.organization_unit_id)
    application.employer_fein = employer_to_map.employer_fein

    service.update_from_request(test_db_session, application_body, application)

    committed_application = (
        test_db_session.query(Application)
        .filter(Application.application_id == application.application_id)
        .one_or_none()
    )

    # Ensure selected organization is not mistakenly mapped to default organization unit
    assert committed_application.organization_unit_id == org_unit.organization_unit_id

    application_body = ApplicationRequestBody(
        organization_unit_selection=OrganizationUnitSelection.not_selected,
        organization_unit_id=None,
    )

    service.update_from_request(test_db_session, application_body, application)

    committed_application = (
        test_db_session.query(Application)
        .filter(Application.application_id == application.application_id)
        .one_or_none()
    )

    # Ensure 'not_selected' is correctly mapped to default organization unit
    assert committed_application.organization_unit_id == org_unit_default.organization_unit_id

    employer_other = EmployerFactory.create()

    application.employer_fein = employer_other.employer_fein
    application_body = ApplicationRequestBody(
        organization_unit_selection=OrganizationUnitSelection.not_selected,
        organization_unit_id=None,
    )

    service.update_from_request(test_db_session, application_body, application)

    # Ensure other employer is not affected by the mapping
    assert (
        committed_application.organization_unit_selection == OrganizationUnitSelection.not_selected
    )
    assert committed_application.organization_unit_id is None


def test_application_updates_has_concurrent_employers(test_db_session, application):
    application_body = ApplicationRequestBody(has_concurrent_employers=True)
    application.has_concurrent_employers = False

    service.update_from_request(test_db_session, application_body, application)
    assert (application.has_concurrent_employers) is True


def test_application_updates_hours_worked_per_week_all_employers(test_db_session, application):
    application_body = ApplicationRequestBody(hours_worked_per_week_all_employers=42.25)
    application.hours_worked_per_week_all_employers = None

    service.update_from_request(test_db_session, application_body, application)
    assert (application.hours_worked_per_week_all_employers) == 42.25


def test_save_fields_to_use_from_user_profile(test_db_session, application):
    fields_to_use_from_user_profile = [
        "gender",
        "gender",
        "raceEthnicity",
        "firstname",
        "lastname",
        "dateOfBirth",
        "addresses",
        "phones",
    ]
    application_body = ApplicationRequestBody(
        fields_to_use_from_user_profile=fields_to_use_from_user_profile,
    )

    service.update_from_request(test_db_session, application_body, application)

    committed_application = (
        test_db_session.query(Application)
        .filter(Application.application_id == application.application_id)
        .one_or_none()
    )

    assert len(committed_application.fields_to_use_from_user_profile) == 7
    assert "gender" in committed_application.fields_to_use_from_user_profile
    assert "raceEthnicity" in committed_application.fields_to_use_from_user_profile
    assert "firstname" in committed_application.fields_to_use_from_user_profile
    assert "lastname" in committed_application.fields_to_use_from_user_profile
    assert "dateOfBirth" in committed_application.fields_to_use_from_user_profile
    assert "addresses" in committed_application.fields_to_use_from_user_profile
    assert "phones" in committed_application.fields_to_use_from_user_profile


def test_create_or_update_experian_address_pair(test_db_session):
    # Create a new address record
    address_id = uuid.uuid4()
    address = Address(
        address_id=address_id,
        address_line_one="123 Main St",
        city="Austin",
        geo_state_id=1,
        zip_code="78701",
        country_id=232,
    )
    test_db_session.add(address)
    test_db_session.commit()

    # Now use this address_id to create or update ExperianAddressPair
    service.create_or_update_experian_address_pair(
        test_db_session, address_id=address_id, is_address_validated=True
    )

    # Fetch the created pair
    experian_address_pair = (
        test_db_session.query(ExperianAddressPair)
        .filter_by(fineos_address_id=address_id)
        .one_or_none()
    )

    assert experian_address_pair is not None
    assert experian_address_pair.fineos_address_id == address_id
    assert experian_address_pair.experian_address_id == address_id


def test_create_or_update_experian_address_pair_not_validated(test_db_session):
    # Create a new address record
    address_id = uuid.uuid4()
    address = Address(
        address_id=address_id,
        address_line_one="456 Elm St",
        city="Boston",
        geo_state_id=1,
        zip_code="02108",
        country_id=232,
    )
    test_db_session.add(address)
    test_db_session.commit()

    # Now use this address_id to create or update ExperianAddressPair without validation
    service.create_or_update_experian_address_pair(
        test_db_session, address_id=address_id, is_address_validated=False
    )

    # Fetch the created pair
    experian_address_pair = (
        test_db_session.query(ExperianAddressPair)
        .filter_by(fineos_address_id=address_id)
        .one_or_none()
    )

    assert experian_address_pair is not None
    assert experian_address_pair.fineos_address_id == address_id
    assert experian_address_pair.experian_address_id is None


def test_previous_leaves_do_not_duplicate(test_db_session, application):
    previous_leave = PreviousLeave(type="any_reason")
    application.previous_leaves = [previous_leave]

    assert len(application.previous_leaves) == 1

    application_body = ApplicationRequestBody(previous_leaves=[PreviousLeave(type="any_reason")])

    service.update_from_request(test_db_session, application_body, application)

    committed_application = (
        test_db_session.query(Application)
        .filter(Application.application_id == application.application_id)
        .one_or_none()
    )

    # Before PFMLPB-11388, this would be 2. (bug)
    assert len(committed_application.previous_leaves) == 1


def test_previous_leaves_removed_if_report_no(test_db_session, application):
    previous_leave = PreviousLeave(type="any_reason")
    application.previous_leaves = [previous_leave]

    assert len(application.previous_leaves) == 1

    application_body = ApplicationRequestBody(
        has_previous_leaves=False, previous_leaves=[PreviousLeave(type="any_reason")]
    )

    service.update_from_request(test_db_session, application_body, application)

    committed_application = (
        test_db_session.query(Application)
        .filter(Application.application_id == application.application_id)
        .one_or_none()
    )

    assert len(committed_application.previous_leaves) == 0


def test_mmg_idv_status_updates(test_db_session, application):
    application_body = ApplicationRequestBody(
        mmg_idv_status="Unverified",
    )
    application.mmg_idv_status_id = MmgIdvStatus.VERIFIED.id

    service.update_from_request(test_db_session, application_body, application)

    committed_application = (
        test_db_session.query(Application)
        .filter(Application.application_id == application.application_id)
        .one_or_none()
    )

    assert committed_application.mmg_idv_status_id == MmgIdvStatus.UNVERIFIED.id


class TestSplitApplicationByDate:
    class TestSplitStartEndDates:
        def test_dates_exclusively_after_split(self):
            with freeze_time("2021-06-14"):
                split_on_date = date.today()
                start_date = split_on_date + timedelta(days=10)
                end_date = split_on_date + timedelta(days=20)
                dates_before, dates_after = service.split_start_end_dates(
                    start_date, end_date, split_on_date
                )
                assert dates_before is None
                assert dates_after is not None
                assert dates_after.start_date == start_date
                assert dates_after.end_date == end_date

        def test_dates_exclusively_before_split(self):
            with freeze_time("2021-06-14"):
                split_on_date = date.today()
                start_date = split_on_date - timedelta(days=20)
                end_date = split_on_date - timedelta(days=10)
                dates_before, dates_after = service.split_start_end_dates(
                    start_date, end_date, split_on_date
                )

                assert dates_after is None
                assert dates_before is not None
                assert dates_before.start_date == start_date
                assert dates_before.end_date == end_date

        def test_start_date_equals_split_date(self):
            with freeze_time("2021-06-14"):
                split_on_date = date.today()
                start_date = split_on_date
                end_date = split_on_date + timedelta(days=20)
                dates_before, dates_after = service.split_start_end_dates(
                    start_date, end_date, split_on_date
                )

                assert dates_before is not None
                assert dates_before.start_date == start_date
                assert dates_before.end_date == start_date
                assert dates_after is not None
                assert dates_after.start_date == split_on_date + timedelta(days=1)
                assert dates_after.end_date == end_date

        def test_end_date_equals_split_date(self):
            with freeze_time("2021-06-14"):
                split_on_date = date.today()
                start_date = split_on_date - timedelta(days=10)
                end_date = split_on_date
                dates_before, dates_after = service.split_start_end_dates(
                    start_date, end_date, split_on_date
                )

                assert dates_before is not None
                assert dates_before.start_date == start_date
                assert dates_before.end_date == end_date
                assert dates_after is None

        def test_end_date_before_start_date(self):
            with freeze_time("2021-06-14"):
                split_on_date = date.today()
                start_date = split_on_date - timedelta(days=10)
                end_date = start_date - timedelta(days=1)
                dates_before, dates_after = service.split_start_end_dates(
                    start_date, end_date, split_on_date
                )

                assert dates_before is None
                assert dates_after is None

        def test_split_date(self):
            with freeze_time("2021-06-14"):
                split_on_date = date.today()
                start_date = split_on_date - timedelta(days=10)
                end_date = split_on_date + timedelta(days=10)
                dates_before, dates_after = service.split_start_end_dates(
                    start_date, end_date, split_on_date
                )

                assert dates_before.start_date == start_date
                assert dates_before.end_date == split_on_date
                assert dates_after.start_date == split_on_date + timedelta(1)
                assert dates_after.end_date == end_date

    def _verify_leave_period(self, leave_period, start_date, end_date, application_id):
        assert leave_period.application_id == application_id

        assert leave_period.start_date == start_date
        assert leave_period.end_date == end_date

    def _verify_benefit(self, benefit, start_date, end_date, amount, application_id):
        assert benefit.application_id == application_id
        if isinstance(benefit, EmployerBenefit):
            assert benefit.benefit_start_date == start_date
            assert benefit.benefit_end_date == end_date
            assert benefit.benefit_amount_dollars == amount
        else:
            assert benefit.income_start_date == start_date
            assert benefit.income_end_date == end_date
            assert benefit.income_amount_dollars == amount

    def test_split_no_leave_periods_or_benefits(
        self, application, test_db_session, application_split
    ):
        split_app_1, split_app_2 = service.split_application_by_date(
            test_db_session, application, application_split
        )
        test_db_session.refresh(split_app_1)
        test_db_session.refresh(split_app_2)

        assert split_app_1.application_id == application.application_id
        assert split_app_2.application_id != application.application_id
        assert split_app_1.application_id != split_app_2.application_id
        assert split_app_1.split_into_application_id == split_app_2.application_id
        assert split_app_2.split_from_application_id == split_app_1.application_id
        assert split_app_1.claim_id is None
        assert split_app_2.claim_id is None

    def test_leave_periods_multiple_dates(self, application, test_db_session, application_split):
        with freeze_time("2021-06-14"):
            split_on_date = date.today()
            start_date_1 = split_on_date - timedelta(days=20)
            end_date_1 = split_on_date - timedelta(days=10)

            start_date_2 = split_on_date - timedelta(days=10)
            end_date_2 = split_on_date + timedelta(days=10)

            start_date_3 = split_on_date + timedelta(days=10)
            end_date_3 = split_on_date + timedelta(days=20)

            application.continuous_leave_periods = [
                ContinuousLeavePeriodFactory.create(
                    start_date=start_date_1,
                    end_date=end_date_1,
                ),
                ContinuousLeavePeriodFactory.create(
                    start_date=start_date_2,
                    end_date=end_date_2,
                ),
                ContinuousLeavePeriodFactory.create(
                    start_date=start_date_3,
                    end_date=end_date_3,
                ),
            ]

            split_app_1, split_app_2 = service.split_application_by_date(
                test_db_session, application, application_split
            )
            test_db_session.refresh(split_app_1)
            test_db_session.refresh(split_app_2)

            # Apply a consistent ordering to the leave periods before validating
            split_app_1_leave_periods = split_app_1.continuous_leave_periods
            split_app_1_leave_periods.sort(key=lambda lp: lp.start_date)
            split_app_2_leave_periods = split_app_2.continuous_leave_periods
            split_app_2_leave_periods.sort(key=lambda lp: lp.start_date)

            assert len(split_app_1.continuous_leave_periods) == 2
            assert len(split_app_2.continuous_leave_periods) == 2
            self._verify_leave_period(
                split_app_1_leave_periods[0], start_date_1, end_date_1, split_app_1.application_id
            )
            self._verify_leave_period(
                split_app_1_leave_periods[1],
                start_date_2,
                split_on_date,
                split_app_1.application_id,
            )
            self._verify_leave_period(
                split_app_2_leave_periods[0],
                split_on_date + timedelta(days=1),
                end_date_2,
                split_app_2.application_id,
            )
            self._verify_leave_period(
                split_app_2_leave_periods[1], start_date_3, end_date_3, split_app_2.application_id
            )

    def test_split_multiple_type_of_leave_periods(
        self, application, test_db_session, application_split
    ):
        with freeze_time("2021-06-14"):
            split_on_date = date.today()
            application.continuous_leave_periods = [
                ContinuousLeavePeriodFactory.create(
                    start_date=split_on_date - timedelta(days=10),
                    end_date=split_on_date + timedelta(days=10),
                )
            ]
            application.intermittent_leave_periods = [
                IntermittentLeavePeriodFactory.create(
                    start_date=split_on_date - timedelta(days=10),
                    end_date=split_on_date + timedelta(days=10),
                )
            ]
            application.reduced_schedule_leave_periods = [
                ReducedScheduleLeavePeriodFactory.create(
                    start_date=split_on_date - timedelta(days=10),
                    end_date=split_on_date + timedelta(days=10),
                )
            ]

            split_app_1, split_app_2 = service.split_application_by_date(
                test_db_session, application, application_split
            )
            test_db_session.refresh(split_app_1)
            test_db_session.refresh(split_app_2)

            assert len(split_app_1.all_leave_periods) == 3
            assert len(split_app_2.all_leave_periods) == 3
            for leave_period in split_app_1.all_leave_periods:
                self._verify_leave_period(
                    leave_period,
                    split_on_date - timedelta(days=10),
                    split_on_date,
                    split_app_1.application_id,
                )

            for leave_period in split_app_2.all_leave_periods:
                self._verify_leave_period(
                    leave_period,
                    split_on_date + timedelta(days=1),
                    split_on_date + timedelta(days=10),
                    split_app_2.application_id,
                )

    def test_multiple_benefits(self, application, test_db_session, application_split):
        with freeze_time("2021-06-14"):
            split_on_date = date.today()
            start_date = split_on_date - timedelta(days=10)
            end_date = split_on_date + timedelta(days=20)

            application.employer_benefits = [
                EmployerBenefitFactory.create(
                    application_id=application.application_id,
                    benefit_start_date=start_date,
                    benefit_end_date=end_date,
                    benefit_amount_dollars=100,
                )
            ]
            application.other_incomes = [
                OtherIncomeFactory.create(
                    application_id=application.application_id,
                    income_start_date=start_date,
                    income_end_date=end_date,
                    income_amount_dollars=200,
                )
            ]

            split_app_1, split_app_2 = service.split_application_by_date(
                test_db_session, application, application_split
            )
            test_db_session.refresh(split_app_1)
            test_db_session.refresh(split_app_2)

            assert len(split_app_1.employer_benefits) == 1
            assert len(split_app_2.employer_benefits) == 1
            assert len(split_app_1.other_incomes) == 1
            assert len(split_app_2.other_incomes) == 1
            for benefit in split_app_1.employer_benefits:
                self._verify_benefit(
                    benefit, start_date, split_on_date, 100, split_app_1.application_id
                )
            for benefit in split_app_2.employer_benefits:
                self._verify_benefit(
                    benefit, split_on_date + timedelta(1), end_date, 100, split_app_2.application_id
                )
            for income in split_app_1.other_incomes:
                self._verify_benefit(
                    income, start_date, split_on_date, 200, split_app_1.application_id
                )
            for income in split_app_2.other_incomes:
                self._verify_benefit(
                    income, split_on_date + timedelta(1), end_date, 200, split_app_2.application_id
                )

    def test_benefit_all_at_once_frequency_scenario_uneven_split(
        self, application, test_db_session, application_split
    ):
        with freeze_time("2021-06-14"):
            split_on_date = date.today()
            start_date = split_on_date - timedelta(days=20)
            end_date = split_on_date + timedelta(days=14)

            application.employer_benefits = [
                EmployerBenefitFactory.create(
                    application_id=application.application_id,
                    benefit_start_date=start_date,
                    benefit_end_date=end_date,
                    benefit_amount_dollars=1000,
                    benefit_amount_frequency_id=AmountFrequency.ALL_AT_ONCE.amount_frequency_id,
                )
            ]

            split_app_1, split_app_2 = service.split_application_by_date(
                test_db_session, application, application_split
            )
            test_db_session.refresh(split_app_1)
            test_db_session.refresh(split_app_2)

            assert len(split_app_1.employer_benefits) == 1
            assert len(split_app_2.employer_benefits) == 1
            for benefit in split_app_1.employer_benefits:
                self._verify_benefit(
                    benefit, start_date, split_on_date, 600, split_app_1.application_id
                )
            for benefit in split_app_2.employer_benefits:
                self._verify_benefit(
                    benefit, split_on_date + timedelta(1), end_date, 400, split_app_2.application_id
                )

    def test_benefit_all_at_once_frequency_scenario_even_split(
        self, application, test_db_session, application_split
    ):
        with freeze_time("2021-06-14"):
            split_on_date = date.today()
            start_date = split_on_date - timedelta(days=4)
            end_date = split_on_date + timedelta(days=5)

            application.employer_benefits = [
                EmployerBenefitFactory.create(
                    application_id=application.application_id,
                    benefit_start_date=start_date,
                    benefit_end_date=end_date,
                    benefit_amount_dollars=1000,
                    benefit_amount_frequency_id=AmountFrequency.ALL_AT_ONCE.amount_frequency_id,
                )
            ]

            split_app_1, split_app_2 = service.split_application_by_date(
                test_db_session, application, application_split
            )
            test_db_session.refresh(split_app_1)
            test_db_session.refresh(split_app_2)

            assert len(split_app_1.employer_benefits) == 1
            assert len(split_app_2.employer_benefits) == 1
            for benefit in split_app_1.employer_benefits:
                self._verify_benefit(
                    benefit, start_date, split_on_date, 500, split_app_1.application_id
                )
            for benefit in split_app_2.employer_benefits:
                self._verify_benefit(
                    benefit, split_on_date + timedelta(1), end_date, 500, split_app_2.application_id
                )

    def test_benefit_multiple_types_and_frequency(
        self, application, test_db_session, application_split
    ):
        with freeze_time("2021-06-14"):
            split_on_date = date.today()
            start_date = split_on_date - timedelta(days=20)
            end_date = split_on_date + timedelta(days=14)

            application.employer_benefits = [
                EmployerBenefitFactory.create(
                    application_id=application.application_id,
                    benefit_start_date=start_date,
                    benefit_end_date=end_date,
                    benefit_amount_dollars=1000,
                    benefit_amount_frequency_id=AmountFrequency.ALL_AT_ONCE.amount_frequency_id,
                ),
                EmployerBenefitFactory.create(
                    application_id=application.application_id,
                    benefit_start_date=start_date,
                    benefit_end_date=end_date,
                    benefit_amount_dollars=300,
                    benefit_amount_frequency_id=AmountFrequency.PER_MONTH.amount_frequency_id,
                ),
            ]
            application.other_incomes = [
                OtherIncomeFactory.create(
                    application_id=application.application_id,
                    income_start_date=start_date,
                    income_end_date=end_date,
                    income_amount_dollars=200,
                    income_amount_frequency_id=AmountFrequency.PER_DAY.amount_frequency_id,
                )
            ]

            split_app_1, split_app_2 = service.split_application_by_date(
                test_db_session, application, application_split
            )
            test_db_session.refresh(split_app_1)
            test_db_session.refresh(split_app_2)

            assert len(split_app_1.employer_benefits) == 2
            assert len(split_app_2.employer_benefits) == 2
            assert len(split_app_1.other_incomes) == 1
            assert len(split_app_2.other_incomes) == 1
            for benefit in split_app_1.employer_benefits:
                amount = (
                    600
                    if benefit.benefit_amount_frequency_id
                    == AmountFrequency.ALL_AT_ONCE.amount_frequency_id
                    else 300
                )
                self._verify_benefit(
                    benefit, start_date, split_on_date, amount, split_app_1.application_id
                )
            for benefit in split_app_2.employer_benefits:
                amount = (
                    400
                    if benefit.benefit_amount_frequency_id
                    == AmountFrequency.ALL_AT_ONCE.amount_frequency_id
                    else 300
                )
                self._verify_benefit(
                    benefit,
                    split_on_date + timedelta(1),
                    end_date,
                    amount,
                    split_app_2.application_id,
                )
            for income in split_app_1.other_incomes:
                self._verify_benefit(
                    income, start_date, split_on_date, 200, split_app_1.application_id
                )
            for income in split_app_2.other_incomes:
                self._verify_benefit(
                    income, split_on_date + timedelta(1), end_date, 200, split_app_2.application_id
                )

    def test_split_app_leave_periods_and_benefits(
        self, application, test_db_session, application_split
    ):
        with freeze_time("2021-06-14"):
            split_on_date = date.today()
            start_date = split_on_date - timedelta(days=20)
            end_date = split_on_date + timedelta(days=14)

            application.continuous_leave_periods = [
                ContinuousLeavePeriodFactory.create(
                    start_date=split_on_date - timedelta(days=10),
                    end_date=split_on_date + timedelta(days=10),
                )
            ]
            application.intermittent_leave_periods = [
                IntermittentLeavePeriodFactory.create(
                    start_date=split_on_date - timedelta(days=10),
                    end_date=split_on_date + timedelta(days=10),
                )
            ]
            application.reduced_schedule_leave_periods = [
                ReducedScheduleLeavePeriodFactory.create(
                    start_date=split_on_date - timedelta(days=10),
                    end_date=split_on_date + timedelta(days=10),
                )
            ]

            application.employer_benefits = [
                EmployerBenefitFactory.create(
                    application_id=application.application_id,
                    benefit_start_date=start_date,
                    benefit_end_date=end_date,
                    benefit_amount_dollars=1000,
                    benefit_amount_frequency_id=AmountFrequency.ALL_AT_ONCE.amount_frequency_id,
                ),
                EmployerBenefitFactory.create(
                    application_id=application.application_id,
                    benefit_start_date=start_date,
                    benefit_end_date=end_date,
                    benefit_amount_dollars=300,
                    benefit_amount_frequency_id=AmountFrequency.PER_MONTH.amount_frequency_id,
                ),
            ]
            application.other_incomes = [
                OtherIncomeFactory.create(
                    application_id=application.application_id,
                    income_start_date=start_date,
                    income_end_date=end_date,
                    income_amount_dollars=200,
                    income_amount_frequency_id=AmountFrequency.PER_DAY.amount_frequency_id,
                )
            ]

            split_app_1, split_app_2 = service.split_application_by_date(
                test_db_session, application, application_split
            )
            test_db_session.commit()
            test_db_session.refresh(split_app_1)
            test_db_session.refresh(split_app_2)

            assert len(split_app_1.all_leave_periods) == 3
            assert split_app_1.has_continuous_leave_periods is True
            assert split_app_1.has_intermittent_leave_periods is True
            assert split_app_1.has_reduced_schedule_leave_periods is True
            assert split_app_1.has_employer_benefits is True
            assert split_app_1.has_other_incomes is True
            assert len(split_app_2.all_leave_periods) == 3
            assert split_app_2.has_continuous_leave_periods is True
            assert split_app_2.has_intermittent_leave_periods is True
            assert split_app_2.has_reduced_schedule_leave_periods is True
            assert split_app_2.has_employer_benefits is True
            assert split_app_2.has_other_incomes is True
            for leave_period in split_app_1.all_leave_periods:
                self._verify_leave_period(
                    leave_period,
                    split_on_date - timedelta(days=10),
                    split_on_date,
                    split_app_1.application_id,
                )

            for leave_period in split_app_2.all_leave_periods:
                self._verify_leave_period(
                    leave_period,
                    split_on_date + timedelta(days=1),
                    split_on_date + timedelta(days=10),
                    split_app_2.application_id,
                )

            for benefit in split_app_1.employer_benefits:
                amount = (
                    600
                    if benefit.benefit_amount_frequency_id
                    == AmountFrequency.ALL_AT_ONCE.amount_frequency_id
                    else 300
                )
                self._verify_benefit(
                    benefit,
                    start_date,
                    split_on_date,
                    amount,
                    split_app_1.application_id,
                )
            for benefit in split_app_2.employer_benefits:
                amount = (
                    400
                    if benefit.benefit_amount_frequency_id
                    == AmountFrequency.ALL_AT_ONCE.amount_frequency_id
                    else 300
                )
                self._verify_benefit(
                    benefit,
                    split_on_date + timedelta(1),
                    end_date,
                    amount,
                    split_app_2.application_id,
                )
            for income in split_app_1.other_incomes:
                self._verify_benefit(
                    income, start_date, split_on_date, 200, split_app_1.application_id
                )
            for income in split_app_2.other_incomes:
                self._verify_benefit(
                    income, split_on_date + timedelta(1), end_date, 200, split_app_2.application_id
                )


class TestUpdateFromRequest:
    def test_application_language_update(self, application, test_db_session):
        application_body = ApplicationRequestBody(
            language=Language.CHINESE_SIMPLIFIED.language_description
        )
        updated_application = service.update_from_request(
            test_db_session, application_body, application
        )

        assert (
            updated_application.language.language_description
            == Language.CHINESE_SIMPLIFIED.language_description
        )
        assert updated_application.language.language_id == Language.CHINESE_SIMPLIFIED.language_id

        committed_application = (
            test_db_session.query(Application)
            .filter(Application.application_id == application.application_id)
            .one_or_none()
        )
        assert (
            committed_application.language.language_description
            == Language.CHINESE_SIMPLIFIED.language_description
        )
        assert committed_application.language.language_id == Language.CHINESE_SIMPLIFIED.language_id


class TestGetPreviousAbsencePeriodFromApplication:
    @pytest.fixture
    def application_with_no_claim(self, user, employee, employer):
        return ApplicationFactory.create(
            user=user,
            employer_fein=employer.employer_fein,
            tax_identifier=employee.tax_identifier,
        )

    @pytest.fixture
    def claim(self, employee, employer):
        return ClaimFactory.create(employer=employer, employee=employee)

    @pytest.fixture
    def second_claim(self, employee, employer):
        return ClaimFactory.create(
            employer=employer,
            employee=employee,
        )

    def test_get_previous_absence_period_with_application(
        self,
        claim,
        second_claim,
        application_with_no_claim,
    ):
        leave_period = [
            ContinuousLeavePeriodFactory.create(
                start_date=date(2023, 5, 1),
                end_date=date(2023, 5, 15),
                application_id=application_with_no_claim.application_id,
            ),
        ]
        application_with_no_claim.leave_periods = leave_period

        previous_claim_1 = claim
        previous_claim_2 = second_claim

        AbsencePeriodFactory.create(
            modified_start_date=date(2023, 1, 20),
            modified_end_date=date(2023, 2, 20),
            absence_period_start_date=date(2023, 1, 2),
            absence_period_end_date=date(2023, 2, 20),
            claim=previous_claim_1,
            absence_period_type_id=AbsencePeriodType.CONTINUOUS.absence_period_type_id,
            leave_request_decision_id=LeaveRequestDecision.APPROVED.leave_request_decision_id,
        )

        AbsencePeriodFactory.create(
            modified_start_date=date(2023, 3, 20),
            modified_end_date=date(2023, 4, 20),
            absence_period_start_date=date(2023, 3, 2),
            absence_period_end_date=date(2023, 4, 20),
            claim=previous_claim_1,
            absence_period_type_id=AbsencePeriodType.CONTINUOUS.absence_period_type_id,
            leave_request_decision_id=LeaveRequestDecision.DENIED.leave_request_decision_id,
        )

        AbsencePeriodFactory.create(
            modified_start_date=date(2022, 1, 20),
            modified_end_date=date(2022, 2, 20),
            absence_period_start_date=date(2022, 1, 2),
            absence_period_end_date=date(2022, 2, 20),
            claim=previous_claim_2,
            absence_period_type_id=AbsencePeriodType.CONTINUOUS.absence_period_type_id,
            leave_request_decision_id=LeaveRequestDecision.APPROVED.leave_request_decision_id,
        )

        # Note: 2/3 absence periods were denied so this logic should only return
        # the single absence period that is approved, in review, or pending and has
        # absence periods start and end dates that fall within the year prior to the
        # earliest start of the application leave period
        previous_absence_periods = get_previous_absence_period_from_claim(application_with_no_claim)
        assert len(previous_absence_periods) == 1


@pytest.mark.parametrize(
    "original_status, current_status, profile_response, expected_log_called",
    [
        pytest.param(
            MmgIdvStatus.VERIFIED.id,
            MmgIdvStatus.UNVERIFIED.id,
            {"firstname": "Patty", "lastname": "PFML", "addresses": [], "phones": []},
            True,
            id="Logging when MMG IDV status is verified and current status is unverified",
        ),
        pytest.param(
            MmgIdvStatus.UNVERIFIED.id,
            MmgIdvStatus.UNVERIFIED.id,
            {"firstname": "Patty", "lastname": "PFML", "addresses": [], "phones": []},
            False,
            id="No logging when MMG IDV status is unverified",
        ),
        pytest.param(
            None,
            None,
            {"firstname": "Patty", "lastname": "PFML", "addresses": [], "phones": []},
            False,
            id="No logging when the current IDV status is None",
        ),
    ],
)
def test_log_mmg_idv_to_application_changes(
    monkeypatch,
    application,
    original_status,
    current_status,
    profile_response,
    expected_log_called,
    caplog,
):
    mock_client = Mock()
    mock_client.get_my_idv_status.return_value = Mock({"summary": {"isVerified": True}})
    mock_client.get_my_profile.return_value = profile_response

    # Patch the get_from_user_idv_status to return our test status
    monkeypatch.setattr(
        service,
        "get_from_user_idv_status",
        lambda client, token: original_status,
    )

    # Setup application status
    application.mmg_idv_status_id = current_status
    application.fields_to_use_from_user_profile = ["firstname", "lastname"]

    # Mock the compare function to return some test data
    monkeypatch.setattr(
        service,
        "compare_application_to_mmg_profile",
        lambda app, profile: (["personal_info"], ["firstname"]),
    )

    service.log_mmg_idv_to_application_changes(application, mock_client, "test-token")

    if expected_log_called:
        assert "mmg_idv_status invalidation summary" in caplog.text
        assert mock_client.get_my_profile.called
    else:
        assert "mmg_idv_status invalidation summary" not in caplog.text
        assert not mock_client.get_my_profile.called


def test_log_mmg_idv_to_application_changes_handles_profile_fetch_error(
    monkeypatch, application, caplog
):
    mock_client = Mock()
    mock_client.get_my_idv_status.return_value = Mock({"summary": {"isVerified": True}})
    mock_client.get_my_profile.return_value = None

    monkeypatch.setattr(
        service,
        "get_from_user_idv_status",
        lambda client, token: MmgIdvStatus.VERIFIED.id,
    )

    application.mmg_idv_status_id = 1

    service.log_mmg_idv_to_application_changes(application, mock_client, "test-token")

    assert "Could not fetch MMG profile for mmg_idv_status invalidation summary" in caplog.text


def test_log_mmg_idv_to_application_changes_handles_exception(monkeypatch, application, caplog):
    mock_client = Mock()
    mock_client.get_my_idv_status.side_effect = Exception("Test error")

    service.log_mmg_idv_to_application_changes(application, mock_client, "test-token")

    assert "Failed to process mmg_idv_status invalidation summary: Test error" in caplog.text


@pytest.mark.parametrize(
    "mmg_idv_status_id, has_state_id, should_upload",
    [
        pytest.param(
            MmgIdvStatus.VERIFIED.id, True, True, id="Should upload when verified and has state ID"
        ),
        pytest.param(
            MmgIdvStatus.UNVERIFIED.id, True, False, id="Should not upload when unverified"
        ),
        pytest.param(
            MmgIdvStatus.VERIFIED.id, False, False, id="Should not upload when no state ID"
        ),
        pytest.param(
            MmgIdvStatus.UNVERIFIED.id,
            False,
            False,
            id="Should not upload when unverified and no state ID",
        ),
    ],
)
def test_upload_placeholder_idv_id(
    test_db_session, application, monkeypatch, mmg_idv_status_id, has_state_id, should_upload
):
    # Mock the document upload service
    mock_upload = Mock()
    monkeypatch.setattr(
        "massgov.pfml.api.services.applications.documents_service.upload_document_to_fineos",
        mock_upload,
    )

    # Set up test application state
    application.mmg_idv_status_id = mmg_idv_status_id
    application.has_state_id = has_state_id

    # Call the function
    service.upload_placeholder_idv_id(application)

    # Verify upload was called or not based on params
    if should_upload:
        assert mock_upload.called
        # Verify the upload was called with correct params
        args = mock_upload.call_args[0]
        assert args[0] == application
        assert args[1].document_type == DocumentType.identification_proof
        assert args[1].name == "mmg_idv_id_proof.png"
        assert args[1].description == "MMG IDV Verified"
        assert args[1].pfml_document_type == DocumentType.mmg_idv_mock_id
        assert isinstance(args[2], FileStorage)
        assert args[2].filename == "mmg_idv_id_proof.png"
        assert args[2].content_type == "image/png"
    else:
        assert not mock_upload.called
