import uuid
from datetime import date, datetime
from typing import Any, Dict
from unittest import mock
from urllib.parse import urlencode

import faker
import pytest
from dateutil.relativedelta import relativedelta
from freezegun.api import freeze_time

import tests.api
from massgov.pfml.api.models.documents.responses import DocumentResponse
from massgov.pfml.db.lookup_data.documents import DocumentType
from massgov.pfml.db.lookup_data.employees import Role
from massgov.pfml.db.lookup_data.language import Language
from massgov.pfml.db.models.employees import UserLeaveAdministrator
from massgov.pfml.db.models.factories import (
    ApplicationFactory,
    EmployeeFactory,
    EmployerFactory,
    EmployerQuarterlyContributionFactory,
    UserFactory,
    VerificationFactory,
)
from massgov.pfml.fineos.exception import FINEOSClientError
from massgov.pfml.fineos.models.customer_api import Base64EncodedFileData
from massgov.pfml.util.strings import format_fein
from tests.api import apply_custom_encoder

fake = faker.Faker()


@pytest.fixture
def valid_claimant_creation_request_body() -> Dict[str, Any]:
    return {
        "email_address": fake.email(domain="example.com"),
        "password": fake.password(length=12),
        "role": {"role_description": "Claimant"},
    }


@pytest.fixture
def employer_for_new_user(initialize_factories_session) -> EmployerFactory:
    return EmployerFactory.create()


@pytest.fixture
def valid_employer_creation_request_body(employer_for_new_user) -> Dict[str, Any]:
    ein = employer_for_new_user.employer_fein

    return {
        "email_address": fake.email(domain="example.com"),
        "password": fake.password(length=12),
        "role": {"role_description": "Employer"},
        "user_leave_administrator": {"employer_fein": format_fein(ein)},
    }


@pytest.fixture
def valid_employer_creation_no_ein_request_body(employer_for_new_user) -> Dict[str, Any]:
    return {
        "email_address": fake.email(domain="example.com"),
        "password": fake.password(length=12),
        "role": {"role_description": "Employer"},
        "user_leave_administrator": {},
    }


@pytest.fixture
def test_verification():
    return VerificationFactory.create()


@pytest.fixture
def user_leave_admin(employer_user, employer, test_verification):
    return UserLeaveAdministrator(
        user_id=employer_user.user_id,
        employer_id=employer.employer_id,
        fineos_web_id="fake-fineos-web-id",
        verification=test_verification,
    )


@pytest.fixture
def user_leave_admin_unverified(employer_user, employer):
    return UserLeaveAdministrator(
        user_id=employer_user.user_id,
        employer_id=employer.employer_id,
    )


def test_users_get(client, employer_user, employer_auth_token, test_db_session):
    employer = EmployerFactory.create()
    link = UserLeaveAdministrator(
        user_id=employer_user.user_id,
        employer_id=employer.employer_id,
        fineos_web_id="fake-fineos-web-id",
    )
    test_db_session.add(link)
    test_db_session.commit()

    response = client.get(
        "/v1/users/{}".format(employer_user.user_id),
        headers={"Authorization": f"Bearer {employer_auth_token}"},
    )
    response_body = response.json()

    assert response.status_code == 200
    assert response_body.get("data")["user_id"] == str(employer_user.user_id)
    assert response_body.get("data")["roles"] == [{"role_description": "Employer", "role_id": 3}]
    assert response_body.get("data")["user_leave_administrators"] == [
        {
            "email_address": employer_user.email_address,
            "user_leave_administrator_id": str(link.user_leave_administrator_id),
            "employer_dba": employer.employer_dba,
            "employer_fein": format_fein(employer.employer_fein),
            "employer_id": str(employer.employer_id),
            "has_fineos_registration": True,
            "verified": False,
            "has_verification_data": False,
        }
    ]


def test_users_get_with_language(client, employer_user, employer_auth_token, test_db_session):
    employer_user.language_id = Language.HAITIAN_CREOLE.language_id
    test_db_session.commit()
    response = client.get(
        "/v1/users/{}".format(employer_user.user_id),
        headers={"Authorization": f"Bearer {employer_auth_token}"},
    )
    response_body = response.json()

    assert response.status_code == 200

    assert (
        response_body.get("data")["language_preference"]
        == Language.HAITIAN_CREOLE.language_description
    )


def test_users_unauthorized_get(client, user, auth_token):
    user_2 = UserFactory.create()

    response = client.get(
        "/v1/users/{}".format(user_2.user_id), headers={"Authorization": f"Bearer {auth_token}"}
    )

    tests.api.validate_error_response(response, 403)


def test_users_get_404(client, auth_token):
    response = client.get(
        "/v1/users/{}".format("0dcb64c8-d259-4169-9b7a-486e5b474bc0"),
        headers={"Authorization": f"Bearer {auth_token}"},
    )
    tests.api.validate_error_response(response, 404)


def test_users_get_fineos_forbidden(client, fineos_user, fineos_user_token):
    # Fineos role cannot access this endpoint
    response = client.get(
        "/v1/users/{}".format(fineos_user.user_id),
        headers={"Authorization": f"Bearer {fineos_user_token}"},
    )
    assert response.status_code == 403


@mock.patch("massgov.pfml.api.users.logger.info")
def test_users_get_current(
    mock_info_logger, client, employer_user, employer_auth_token, test_db_session
):
    employer = EmployerFactory.create()
    link = UserLeaveAdministrator(
        user_id=employer_user.user_id,
        employer_id=employer.employer_id,
        fineos_web_id="fake-fineos-web-id",
    )
    test_db_session.add(link)
    test_db_session.commit()

    response = client.get(
        "/v1/users/current", headers={"Authorization": f"Bearer {employer_auth_token}"}
    )
    response_body = response.json()

    test_db_session.refresh(employer_user)
    assert response.status_code == 200
    assert response_body.get("data")["user_id"] == str(employer_user.user_id)
    assert response_body.get("data")["roles"] == [{"role_description": "Employer", "role_id": 3}]
    assert response_body.get("data")["user_leave_administrators"] == [
        {
            "email_address": employer_user.email_address,
            "user_leave_administrator_id": str(link.user_leave_administrator_id),
            "employer_dba": employer.employer_dba,
            "employer_fein": format_fein(employer.employer_fein),
            "employer_id": str(employer.employer_id),
            "has_fineos_registration": True,
            "verified": False,
            "has_verification_data": False,
        }
    ]
    assert mock_info_logger.call_count == 1
    msg_arg, kwargs = mock_info_logger.call_args_list[0]
    assert msg_arg[0] == "users_current_get - has unverified org"
    assert kwargs["extra"]["user_id"] == employer_user.user_id
    assert kwargs["extra"]["has_unverified_org"] == "True"


@mock.patch("massgov.pfml.api.users.logger.info")
def test_users_get_current_log_verified(
    mock_info_logger, client, employer_user, employer_auth_token, test_db_session
):
    employer = EmployerFactory.create()
    link = UserLeaveAdministrator(
        user_id=employer_user.user_id,
        employer_id=employer.employer_id,
        fineos_web_id="fake-fineos-web-id",
        verification=VerificationFactory.create(),
    )
    test_db_session.add(link)
    test_db_session.commit()

    response = client.get(
        "/v1/users/current", headers={"Authorization": f"Bearer {employer_auth_token}"}
    )
    response_body = response.json()

    assert response.status_code == 200
    assert response_body.get("data")["user_leave_administrators"] == [
        {
            "email_address": employer_user.email_address,
            "user_leave_administrator_id": str(link.user_leave_administrator_id),
            "employer_dba": employer.employer_dba,
            "employer_fein": format_fein(employer.employer_fein),
            "employer_id": str(employer.employer_id),
            "has_fineos_registration": True,
            "verified": True,
            "has_verification_data": False,
        }
    ]
    assert mock_info_logger.call_count == 1
    msg_arg, kwargs = mock_info_logger.call_args_list[0]
    assert msg_arg[0] == "users_current_get - has unverified org"
    assert kwargs["extra"]["user_id"] == employer_user.user_id
    assert kwargs["extra"]["has_unverified_org"] == "False"


@mock.patch("massgov.pfml.api.users.logger.info")
def test_users_get_current_log_mixed_verification(
    mock_info_logger, client, employer_user, employer_auth_token, test_db_session
):
    employer_one = EmployerFactory.create()
    link_one = UserLeaveAdministrator(
        user_id=employer_user.user_id,
        employer_id=employer_one.employer_id,
        fineos_web_id="fake-fineos-web-id",
        verification=VerificationFactory.create(),
    )
    test_db_session.add(link_one)
    test_db_session.commit()

    employer_two = EmployerFactory.create()
    link_two = UserLeaveAdministrator(
        user_id=employer_user.user_id,
        employer_id=employer_two.employer_id,
        fineos_web_id="fake-fineos-web-id",
    )
    test_db_session.add(link_two)
    test_db_session.commit()

    response = client.get(
        "/v1/users/current", headers={"Authorization": f"Bearer {employer_auth_token}"}
    )
    response_body = response.json()

    assert response.status_code == 200
    assert {
        "email_address": employer_user.email_address,
        "user_leave_administrator_id": str(link_one.user_leave_administrator_id),
        "employer_dba": employer_one.employer_dba,
        "employer_fein": format_fein(employer_one.employer_fein),
        "employer_id": str(employer_one.employer_id),
        "has_fineos_registration": True,
        "verified": True,
        "has_verification_data": False,
    } in response_body.get("data")["user_leave_administrators"]
    assert mock_info_logger.call_count == 1
    msg_arg, kwargs = mock_info_logger.call_args_list[0]
    assert msg_arg[0] == "users_current_get - has unverified org"
    assert kwargs["extra"]["user_id"] == employer_user.user_id
    assert kwargs["extra"]["has_unverified_org"] == "True"


@mock.patch("massgov.pfml.api.users.logger.info")
def test_users_get_current_log_claimant(
    mock_info_logger, client, user, auth_token, test_db_session
):
    response = client.get("/v1/users/current", headers={"Authorization": f"Bearer {auth_token}"})

    assert response.status_code == 200
    assert mock_info_logger.call_count == 0


def test_users_get_current_has_multiple_tax_identifiers(
    client, consented_user, consented_user_token, test_db_session
):
    employee1 = EmployeeFactory.create()
    employee2 = EmployeeFactory.create()
    ApplicationFactory.create(
        user=consented_user, submitted_time=date.today(), tax_identifier=employee1.tax_identifier
    )
    ApplicationFactory.create(
        user=consented_user, submitted_time=date.today(), tax_identifier=employee2.tax_identifier
    )
    test_db_session.commit()
    response = client.get(
        "/v1/users/current", headers={"Authorization": f"Bearer {consented_user_token}"}
    )
    response_body = response.json()

    assert response.status_code == 200
    assert response_body.get("data")["user_id"] == str(consented_user.user_id)
    assert response_body.get("data")["has_multiple_tax_identifiers"] is True


def test_users_get_current_has_multiple_tax_identifiers_one_null_tax_identifier(
    client, consented_user, consented_user_token, test_db_session
):
    employee1 = EmployeeFactory.create()
    ApplicationFactory.create(
        user=consented_user, submitted_time=date.today(), tax_identifier=employee1.tax_identifier
    )
    ApplicationFactory.create(
        user=consented_user,
        submitted_time=date.today(),
        tax_identifier=None,
        tax_identifier_id=None,
    )
    test_db_session.commit()
    response = client.get(
        "/v1/users/current", headers={"Authorization": f"Bearer {consented_user_token}"}
    )
    response_body = response.json()

    assert response.status_code == 200
    assert response_body.get("data")["user_id"] == str(consented_user.user_id)
    assert response_body.get("data")["has_multiple_tax_identifiers"] is False


def test_users_get_current_has_multiple_tax_identifiers_one_unsubmitted_application(
    client, consented_user, consented_user_token, test_db_session
):
    employee1 = EmployeeFactory.create()
    ApplicationFactory.create(
        user=consented_user, submitted_time=date.today(), tax_identifier=employee1.tax_identifier
    )
    ApplicationFactory.create(user=consented_user, submitted_time=None)
    test_db_session.commit()
    response = client.get(
        "/v1/users/current", headers={"Authorization": f"Bearer {consented_user_token}"}
    )
    response_body = response.json()

    assert response.status_code == 200
    assert response_body.get("data")["user_id"] == str(consented_user.user_id)
    assert response_body.get("data")["has_multiple_tax_identifiers"] is False


def test_users_get_current_no_user_unauthorized(client):
    response = client.get("/v1/users/current")
    assert response.status_code == 401


def test_users_get_current_with_query_count(
    client, employer_user, employer_auth_token, test_db_session, sqlalchemy_query_counter
):
    """
    assert that the number of database queries in get_current_user and user_response
    is independent of the number of leave admin objects and quarterly contributions objects
    a user has
    """
    for _ in range(100):
        employer = EmployerFactory.create()
        for _ in range(10):
            EmployerQuarterlyContributionFactory.create(employer_id=employer.employer_id)
        link = UserLeaveAdministrator(
            user_id=employer_user.user_id,
            employer_id=employer.employer_id,
            fineos_web_id="fake-fineos-web-id",
        )
        test_db_session.add(link)
    test_db_session.commit()

    with sqlalchemy_query_counter(test_db_session, expected_query_count=9):
        response = client.get(
            "/v1/users/current", headers={"Authorization": f"Bearer {employer_auth_token}"}
        )

    assert response.status_code == 200


def test_users_get_current_fineos_forbidden(client, fineos_user_token):
    # Fineos role cannot access this endpoint
    response = client.get(
        "/v1/users/current", headers={"Authorization": f"Bearer {fineos_user_token}"}
    )
    assert response.status_code == 403


def test_users_get_current_deactivated_leave_admins_excluded(
    client, employer_user, employer_auth_token, test_db_session
):
    employer = EmployerFactory.create()
    link = UserLeaveAdministrator(
        user_id=employer_user.user_id,
        employer_id=employer.employer_id,
        fineos_web_id="fake-fineos-web-id",
        deactivated=True,
    )
    test_db_session.add(link)
    test_db_session.commit()

    response = client.get(
        "/v1/users/{}".format(employer_user.user_id),
        headers={"Authorization": f"Bearer {employer_auth_token}"},
    )
    response_body = response.json()

    assert response.status_code == 200
    assert response_body.get("data")["user_id"] == str(employer_user.user_id)
    assert response_body.get("data")["roles"] == [{"role_description": "Employer", "role_id": 3}]
    assert response_body.get("data")["user_leave_administrators"] == []


def test_users_patch(client, user, auth_token, test_db_session):
    assert user.consented_to_data_sharing is False
    body = {"consented_to_data_sharing": True}
    response = client.patch(
        "v1/users/{}".format(user.user_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=body,
    )
    response_body = response.json()
    assert response.status_code == 200
    assert response_body.get("data")["consented_to_data_sharing"] is True

    test_db_session.refresh(user)
    assert user.consented_to_data_sharing is True


def test_users_patch_with_language(client, user, auth_token, test_db_session):
    body = {"language_preference": "Chinese (simplified)"}
    response = client.patch(
        "v1/users/{}".format(user.user_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=body,
    )
    test_db_session.refresh(user)

    response_body = response.json()
    assert response.status_code == 200

    assert response_body.get("data")["language_preference"] == "Chinese (simplified)"
    assert user.language.language_id == Language.CHINESE_SIMPLIFIED.language_id


@mock.patch(
    "massgov.pfml.fineos.mock_client.MockFINEOSClient.create_or_update_leave_admin",
)
def test_users_patch_leave_admin_data(
    mock_fineos_call, client, employer_user, employer_auth_token, test_db_session, user_leave_admin
):
    assert employer_user.first_name is None
    assert employer_user.last_name is None
    assert employer_user.phone_number is None
    employer_user.user_leave_administrators = [user_leave_admin]
    test_db_session.commit()
    body = {
        "first_name": "Slinky",
        "last_name": "Glenesk",
        "phone": {"phone_number": "************", "int_code": "1", "extension": "123"},
    }
    response = client.patch(
        "v1/users/{}".format(employer_user.user_id),
        headers={"Authorization": f"Bearer {employer_auth_token}"},
        json=body,
    )
    test_db_session.refresh(employer_user)
    response_body = response.json()
    assert response.status_code == 200
    assert response_body.get("data")["first_name"] == "Slinky"
    assert response_body.get("data")["last_name"] == "Glenesk"
    assert response_body.get("data")["phone_number"] == {
        "int_code": "1",
        "phone_number": "***-***-3889",
        "phone_type": None,
        "extension": "123",
    }
    assert employer_user.first_name == "Slinky"
    assert employer_user.last_name == "Glenesk"
    assert employer_user.phone_number == "+18056103889"
    assert employer_user.phone_extension == "123"
    mock_fineos_call.assert_called_once()


@mock.patch("massgov.pfml.api.services.users.update_leave_admin_with_fineos")
def test_users_patch_leave_admin_update_not_called_if_unverified(
    mock_update_la,
    client,
    employer_user,
    employer_auth_token,
    user_leave_admin_unverified,
    test_db_session,
):
    employer_user.user_leave_administrators = [user_leave_admin_unverified]
    test_db_session.commit()
    body = {
        "first_name": "Slinky",
        "last_name": "Glenesk",
        "phone": {"phone_number": "************", "int_code": "1", "extension": "123"},
    }
    response = client.patch(
        "v1/users/{}".format(employer_user.user_id),
        headers={"Authorization": f"Bearer {employer_auth_token}"},
        json=body,
    )

    mock_update_la.assert_not_called()
    assert response.status_code == 200


def test_users_patch_leave_admin_data_rejects_invalid_extension(
    client, employer_user, employer_auth_token, test_db_session
):
    assert employer_user.first_name is None
    assert employer_user.last_name is None
    assert employer_user.phone_number is None
    body = {
        "first_name": "Slinky",
        "last_name": "Glenesk",
        "phone": {"phone_number": "************", "int_code": "1", "extension": "ext 123"},
    }
    response = client.patch(
        "v1/users/{}".format(employer_user.user_id),
        headers={"Authorization": f"Bearer {employer_auth_token}"},
        json=body,
    )
    response_body = response.json()
    assert response_body["errors"][0]["field"] == "phone.extension"
    assert response_body["errors"][0]["type"] == "pattern"
    assert response.status_code == 400


@mock.patch(
    "massgov.pfml.fineos.mock_client.MockFINEOSClient.create_or_update_leave_admin",
    side_effect=Exception(),
)
def test_users_patch_leave_admin_data_fineos_failure_fails_gracefully(
    mock_fineos_update, client, employer_user, employer_auth_token, test_db_session
):
    assert employer_user.first_name is None
    assert employer_user.last_name is None
    assert employer_user.phone_number is None
    body = {
        "first_name": "Slinky",
        "last_name": "Glenesk",
        "phone": {"phone_number": "************", "int_code": "1", "extension": "123"},
    }
    response = client.patch(
        "v1/users/{}".format(employer_user.user_id),
        headers={"Authorization": f"Bearer {employer_auth_token}"},
        json=body,
    )
    test_db_session.refresh(employer_user)
    response_body = response.json()
    assert response.status_code == 200
    assert response_body.get("data")["first_name"] == "Slinky"
    assert response_body.get("data")["last_name"] == "Glenesk"
    assert response_body.get("data")["phone_number"] == {
        "int_code": "1",
        "phone_number": "***-***-3889",
        "phone_type": None,
        "extension": "123",
    }
    assert employer_user.first_name == "Slinky"
    assert employer_user.last_name == "Glenesk"
    assert employer_user.phone_number == "+18056103889"
    assert employer_user.phone_extension == "123"


@pytest.mark.parametrize(
    "request_body, error_field",
    [
        [
            {
                "first_name": "Slinky",
                "phone": {
                    "phone_number": "************",
                    "int_code": "1",
                    "extension": "123",
                },
            },
            "last_name",
        ],
        [
            {
                "last_name": "Glenesk",
                "phone": {
                    "phone_number": "************",
                    "int_code": "1",
                    "extension": "123",
                },
            },
            "first_name",
        ],
        [
            {
                "last_name": "Glenesk",
                "first_name": "Slinky",
            },
            "phone.phone_number",
        ],
        [
            {
                "last_name": "Glenesk",
                "first_name": "Slinky",
                "phone": {"phone_number": None},
            },
            "phone.phone_number",
        ],
    ],
)
def test_users_patch_leave_admin_data_validates_requirements(
    request_body,
    error_field,
    client,
    employer_user,
    employer_auth_token,
):
    response = client.patch(
        "v1/users/{}".format(employer_user.user_id),
        headers={"Authorization": f"Bearer {employer_auth_token}"},
        json=request_body,
    )
    response_body = response.json()
    assert response.status_code == 400
    assert response_body["errors"][0]["type"] == "required"

    assert response_body["errors"][0]["field"] == error_field


@pytest.mark.parametrize(
    "phone, err_type",
    [["123", "pattern"], [None, "required"], ["***-***-1111", "invalid_masked_field"]],
)
def test_users_patch_leave_admin_data_validates_phone(
    phone, err_type, client, employer_user, employer_auth_token, test_db_session
):
    employer_user.phone_number = "+18056103889"
    test_db_session.commit()
    body = {"last_name": "Glenesk", "first_name": "Slinky", "phone": {"phone_number": phone}}
    response = client.patch(
        "v1/users/{}".format(employer_user.user_id),
        headers={"Authorization": f"Bearer {employer_auth_token}"},
        json=body,
    )
    response_body = response.json()
    assert response.status_code == 400
    assert response_body["errors"][0]["type"] == err_type
    assert response_body["errors"][0]["field"] == "phone.phone_number"


@mock.patch(
    "massgov.pfml.fineos.mock_client.MockFINEOSClient.create_or_update_leave_admin",
)
def test_users_patch_leave_admin_accepts_same_masked_phone(
    mock_fineos_update,
    client,
    employer_user,
    employer_auth_token,
    test_db_session,
    user_leave_admin,
):
    employer_user.phone_number = "+18056103889"
    employer_user.user_leave_administrators = [user_leave_admin]
    test_db_session.commit()
    body = {
        "last_name": "Glenesk",
        "first_name": "Slinky",
        "phone": {"phone_number": "***-***-3889"},
    }
    response = client.patch(
        "v1/users/{}".format(employer_user.user_id),
        headers={"Authorization": f"Bearer {employer_auth_token}"},
        json=body,
    )
    response_body = response.json()
    assert response.status_code == 200
    assert response_body.get("data")["first_name"] == "Slinky"
    assert response_body.get("data")["last_name"] == "Glenesk"
    assert response_body.get("data")["phone_number"] == {
        "int_code": "1",
        "phone_number": "***-***-3889",
        "phone_type": None,
        "extension": None,
    }
    assert employer_user.phone_number == "+18056103889"
    mock_fineos_update.assert_called_once()


@mock.patch(
    "massgov.pfml.fineos.mock_client.MockFINEOSClient.create_or_update_leave_admin",
)
def test_users_patch_leave_admin_can_delete_existing_extension(
    mock_fineos_update,
    client,
    employer_user,
    employer_auth_token,
    test_db_session,
    user_leave_admin,
):
    employer_user.phone_number = "+18056103889"
    employer_user.phone_extension = "333"
    employer_user.user_leave_administrators = [user_leave_admin]
    test_db_session.commit()
    body = {
        "last_name": "Glenesk",
        "first_name": "Slinky",
        "phone": {"phone_number": "***-***-3889", "int_code": "1", "extension": None},
    }
    response = client.patch(
        "v1/users/{}".format(employer_user.user_id),
        headers={"Authorization": f"Bearer {employer_auth_token}"},
        json=body,
    )
    test_db_session.refresh(employer_user)
    response_body = response.json()
    assert response.status_code == 200
    assert response_body.get("data")["first_name"] == "Slinky"
    assert response_body.get("data")["last_name"] == "Glenesk"
    assert response_body.get("data")["phone_number"] == {
        "int_code": "1",
        "phone_number": "***-***-3889",
        "phone_type": None,
        "extension": None,
    }
    assert employer_user.phone_number == "+18056103889"
    assert employer_user.phone_extension is None
    mock_fineos_update.assert_called_once()


def test_users_patch_leave_admin_accepts_same_masked_phone_and_commits_new_extension(
    client, employer_user, employer_auth_token, test_db_session
):
    employer_user.phone_number = "+18056103889"
    employer_user.phone_extension = "123"
    test_db_session.commit()
    body = {
        "last_name": "Glenesk",
        "first_name": "Slinky",
        "phone": {"phone_number": "***-***-3889", "extension": "321"},
    }
    response = client.patch(
        "v1/users/{}".format(employer_user.user_id),
        headers={"Authorization": f"Bearer {employer_auth_token}"},
        json=body,
    )
    test_db_session.refresh(employer_user)
    response_body = response.json()
    assert response.status_code == 200
    assert response_body.get("data")["first_name"] == "Slinky"
    assert response_body.get("data")["last_name"] == "Glenesk"
    assert response_body.get("data")["phone_number"] == {
        "int_code": "1",
        "phone_number": "***-***-3889",
        "phone_type": None,
        "extension": "321",
    }
    assert employer_user.phone_number == "+18056103889"
    assert employer_user.phone_extension == "321"


def test_users_patch_leave_admin_data_extension_deletion(
    client, employer_user, employer_auth_token, test_db_session
):
    employer_user.phone_extension = "111"
    test_db_session.commit()
    body = {
        "last_name": "Glenesk",
        "first_name": "Slinky",
        "phone": {"phone_number": "************", "extension": None, "int_code": "1"},
    }
    response = client.patch(
        "v1/users/{}".format(employer_user.user_id),
        headers={"Authorization": f"Bearer {employer_auth_token}"},
        json=body,
    )
    test_db_session.refresh(employer_user)
    response_body = response.json()
    assert response.status_code == 200
    assert response_body.get("data")["first_name"] == "Slinky"
    assert response_body.get("data")["last_name"] == "Glenesk"
    assert response_body.get("data")["phone_number"] == {
        "int_code": "1",
        "phone_number": "***-***-3889",
        "phone_type": None,
        "extension": None,
    }
    assert employer_user.phone_extension is None


def test_users_patch_leave_admin_data_prohibits_claimant_user(
    client,
    user,
    auth_token,
):
    body = {
        "first_name": "Slinky",
        "last_name": "Glenesk",
        "phone": {"phone_number": "************", "int_code": "1", "extension": "123"},
    }
    response = client.patch(
        "v1/users/{}".format(user.user_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=body,
    )
    assert response.status_code == 403
    assert user.first_name is None


def test_users_convert_employer(client, user, employer_for_new_user, auth_token, test_db_session):
    ein = employer_for_new_user.employer_fein
    body = {"employer_fein": format_fein(ein)}
    assert len(user.roles) == 0
    response = client.post(
        "v1/users/{}/convert-employer".format(user.user_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=body,
    )
    response_body = response.json()
    assert response.status_code == 201
    assert response_body.get("data")["user_leave_administrators"] == [
        {
            "email_address": user.email_address,
            "user_leave_administrator_id": str(
                user.user_leave_administrators[0].user_leave_administrator_id
            ),
            "employer_dba": employer_for_new_user.employer_dba,
            "employer_fein": format_fein(employer_for_new_user.employer_fein),
            "employer_id": str(employer_for_new_user.employer_id),
            "has_fineos_registration": False,
            "has_verification_data": False,
            "verified": False,
        }
    ]
    assert response_body.get("data")["roles"] == [
        {"role_description": "Employer", "role_id": Role.EMPLOYER.role_id}
    ]

    test_db_session.refresh(user)

    assert len(user.roles) == 1
    assert user.roles[0].role_id == Role.EMPLOYER.role_id
    assert len(user.user_leave_administrators) == 1
    assert user.user_leave_administrators[0].employer_id == employer_for_new_user.employer_id


def test_users_convert_employer_bad_fein(client, user, auth_token):
    body = {"employer_fein": "99-9999999"}
    response = client.post(
        "v1/users/{}/convert-employer".format(user.user_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=body,
    )
    tests.api.validate_error_response(response, 400)
    errors = response.json().get("errors")

    assert {
        "field": "employer_fein",
        "message": "Invalid FEIN",
        "type": "require_employer",
    } in errors


def test_users_unauthorized_convert_employer(
    client, employer_for_new_user, auth_token, test_db_session
):
    user_2 = UserFactory.create()
    assert len(user_2.user_leave_administrators) == 0
    ein = employer_for_new_user.employer_fein
    body = {"employer_fein": format_fein(ein)}
    response = client.post(
        "v1/users/{}/convert-employer".format(user_2.user_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=body,
    )

    tests.api.validate_error_response(response, 403)

    assert len(user_2.user_leave_administrators) == 0


def test_users_unauthorized_patch(client, user, auth_token, test_db_session):
    user_2 = UserFactory.create()

    assert user_2.consented_to_data_sharing is False
    body = {"consented_to_data_sharing": True}
    response = client.patch(
        "v1/users/{}".format(user_2.user_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=body,
    )

    tests.api.validate_error_response(response, 403)

    assert user_2.consented_to_data_sharing is False


@pytest.mark.parametrize(
    "request_body, expected_code",
    [
        # fail if field is invalid
        ({"email_address": "<EMAIL>"}, 400),
        # fail if there are multiple fields
        (
            {
                "consented_to_data_sharing": False,
                "email_address": "<EMAIL>",
                "auth_id": uuid.uuid4(),
            },
            400,
        ),
    ],
)
def test_users_patch_invalid(client, user, auth_token, request_body, expected_code):
    response = client.patch(
        "/v1/users/{}".format(user.user_id),
        json=apply_custom_encoder(request_body),
        headers={"Authorization": f"Bearer {auth_token}"},
    )

    assert response.status_code == expected_code


def test_users_patch_404(client, auth_token):
    body = {"consented_to_data_sharing": True}
    response = client.patch(
        "/v1/users/{}".format("00000000-0000-0000-0000-000000000000"),
        json=body,
        headers={"Authorization": f"Bearer {auth_token}"},
    )

    tests.api.validate_error_response(response, 404)


def test_users_patch_fineos_forbidden(client, fineos_user, fineos_user_token):
    # Fineos role cannot access this endpoint
    body = {"consented_to_data_sharing": True}
    response = client.patch(
        "v1/users/{}".format(fineos_user.user_id),
        headers={"Authorization": f"Bearer {fineos_user_token}"},
        json=body,
    )
    tests.api.validate_error_response(response, 403)


def test_users_patch_consent_to_view_tax_documents(client, user, auth_token, test_db_session):
    assert user.consented_to_view_tax_documents is None
    body = {"consented_to_view_tax_documents": True}
    response = client.patch(
        "v1/users/{}".format(user.user_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=body,
    )
    response_body = response.json()
    assert response.status_code == 200
    assert response_body.get("data")["consented_to_view_tax_documents"] is True

    test_db_session.refresh(user)
    assert user.consented_to_view_tax_documents is True


def test_has_verification_data_flag(client, employer_user, employer_auth_token, test_db_session):
    employer = EmployerFactory.create()
    # yesterday = datetime.today() - relativedelta(days=1)
    yesterday = date.today() - relativedelta(days=1)
    EmployerQuarterlyContributionFactory.create(employer=employer, filing_period=yesterday)
    link = UserLeaveAdministrator(
        user_id=employer_user.user_id,
        employer_id=employer.employer_id,
        fineos_web_id="fake-fineos-web-id",
    )
    test_db_session.add(link)
    test_db_session.commit()

    response = client.get(
        "/v1/users/{}".format(employer_user.user_id),
        headers={"Authorization": f"Bearer {employer_auth_token}"},
    )
    response_body = response.json()

    assert response_body.get("data")["user_leave_administrators"] == [
        {
            "email_address": employer_user.email_address,
            "user_leave_administrator_id": str(link.user_leave_administrator_id),
            "employer_dba": employer.employer_dba,
            "employer_fein": format_fein(employer.employer_fein),
            "employer_id": str(employer.employer_id),
            "has_fineos_registration": True,
            "verified": False,
            "has_verification_data": True,
        }
    ]


@freeze_time("2021-05-01")
def test_has_verification_data_flag_future_data(
    client, employer_user, employer_auth_token, test_db_session
):
    employer = EmployerFactory.create()
    EmployerQuarterlyContributionFactory.create(employer=employer, filing_period="2021-06-01")
    link = UserLeaveAdministrator(
        user_id=employer_user.user_id,
        employer_id=employer.employer_id,
        fineos_web_id="fake-fineos-web-id",
    )
    test_db_session.add(link)
    test_db_session.commit()

    response = client.get(
        "/v1/users/{}".format(employer_user.user_id),
        headers={"Authorization": f"Bearer {employer_auth_token}"},
    )
    response_body = response.json()

    assert response_body.get("data")["user_leave_administrators"] == [
        {
            "email_address": employer_user.email_address,
            "user_leave_administrator_id": str(link.user_leave_administrator_id),
            "employer_dba": employer.employer_dba,
            "employer_fein": format_fein(employer.employer_fein),
            "employer_id": str(employer.employer_id),
            "has_fineos_registration": True,
            "verified": False,
            "has_verification_data": True,
        }
    ]


def test_has_verification_data_flag_old_data(
    client, employer_user, employer_auth_token, test_db_session
):
    employer = EmployerFactory.create()
    EmployerQuarterlyContributionFactory.create(employer=employer, filing_period="2019-02-18")
    link = UserLeaveAdministrator(
        user_id=employer_user.user_id,
        employer_id=employer.employer_id,
        fineos_web_id="fake-fineos-web-id",
    )
    test_db_session.add(link)
    test_db_session.commit()

    response = client.get(
        "/v1/users/{}".format(employer_user.user_id),
        headers={"Authorization": f"Bearer {employer_auth_token}"},
    )
    response_body = response.json()

    assert response_body.get("data")["user_leave_administrators"] == [
        {
            "email_address": employer_user.email_address,
            "user_leave_administrator_id": str(link.user_leave_administrator_id),
            "employer_dba": employer.employer_dba,
            "employer_fein": format_fein(employer.employer_fein),
            "employer_id": str(employer.employer_id),
            "has_fineos_registration": True,
            "verified": False,
            "has_verification_data": False,
        }
    ]


def test_get_has_usable_data_for_application_auth(client, consented_user):
    response_unauthorized = client.get(
        "/v1/users/{}/profile/has-usable-data-for-application".format(consented_user.user_id),
    )
    assert response_unauthorized.status_code == 401


def test_get_has_usable_data_for_application(client, consented_user, consented_user_token):
    with mock.patch(
        "massgov.pfml.api.app.get_features_config",
        return_value=mock.Mock(universal_profile=mock.Mock(enable_universal_profile_idv=False)),
    ):
        response = client.get(
            "/v1/users/{}/profile/has-usable-data-for-application".format(consented_user.user_id),
            headers={"Authorization": f"Bearer {consented_user_token}"},
        )

        assert response.status_code == 200
        response_body = response.json()

        assert "gender" in response_body.get("data")["usable_fields"]
        assert "raceEthnicity" in response_body.get("data")["usable_fields"]
        assert "firstname" not in response_body.get("data")["usable_fields"]
        assert "lastname" not in response_body.get("data")["usable_fields"]
        assert "dateOfBirth" not in response_body.get("data")["usable_fields"]
        assert "addresses" not in response_body.get("data")["usable_fields"]
        assert "phones" not in response_body.get("data")["usable_fields"]


def test_get_has_usable_data_for_application_with_idv_include(
    client, consented_user, consented_user_token
):
    with mock.patch(
        "massgov.pfml.api.app.get_features_config",
        return_value=mock.Mock(universal_profile=mock.Mock(enable_universal_profile_idv=True)),
    ):
        response = client.get(
            "/v1/users/{}/profile/has-usable-data-for-application".format(consented_user.user_id),
            headers={"Authorization": f"Bearer {consented_user_token}"},
        )

    assert response.status_code == 200
    response_body = response.json()

    assert "gender" in response_body.get("data")["usable_fields"]
    assert "raceEthnicity" in response_body.get("data")["usable_fields"]
    assert "firstname" in response_body.get("data")["usable_fields"]
    assert "lastname" in response_body.get("data")["usable_fields"]
    assert "dateOfBirth" in response_body.get("data")["usable_fields"]
    assert "addresses" in response_body.get("data")["usable_fields"]
    assert "phones" in response_body.get("data")["usable_fields"]


def test_patch_user_profile_unauthorized_user(
    client, test_db_session, consented_user, consented_user_token
):
    user_2 = UserFactory.create()
    application = ApplicationFactory.create(user=user_2, submitted_time=datetime.now())
    test_db_session.commit()

    body = {
        "from_application": application.application_id,
        "profile_fields_include": ["raceEthnicity", "gender"],
    }

    response = client.patch(
        "/v1/users/{}/profile".format(consented_user.user_id),
        headers={"Authorization": f"Bearer {consented_user_token}"},
        json=apply_custom_encoder(body),
    )
    assert response.status_code == 403


def test_patch_user_profile_unauthorized_application(
    client, test_db_session, consented_user, consented_user_token
):
    ApplicationFactory.create(user=consented_user, submitted_time=datetime.now())
    application2 = ApplicationFactory.create(submitted_time=datetime.now())

    test_db_session.commit()

    body = {
        "from_application": application2.application_id,
        "profile_fields_include": ["raceEthnicity", "gender"],
    }

    response = client.patch(
        "/v1/users/{}/profile".format(consented_user.user_id),
        headers={"Authorization": f"Bearer {consented_user_token}"},
        json=apply_custom_encoder(body),
    )
    assert response.status_code == 403


def test_patch_user_profile(
    client, monkeypatch, test_db_session, consented_user, consented_user_token, application
):
    monkeypatch.setenv("ENVIRONMENT", "local")
    monkeypatch.setenv("MY_MASS_GOV_API_CLIENT_MOCK_CALLS", "1")
    application = ApplicationFactory.create(user=consented_user, submitted_time=datetime.now())
    test_db_session.commit()

    body = {
        "from_application": application.application_id,
        "profile_fields_include": ["raceEthnicity", "gender"],
    }

    response = client.patch(
        "/v1/users/{}/profile".format(consented_user.user_id),
        headers={"Authorization": f"Bearer {consented_user_token}"},
        json=apply_custom_encoder(body),
    )

    assert response.status_code == 200


class TestDocumentsGet:
    @pytest.fixture(autouse=True)
    def user_with_submitted_application(self, consented_user):
        ApplicationFactory.create(user=consented_user, submitted_time=datetime.now())
        return consented_user

    @pytest.fixture(autouse=True)
    def documents(self, consented_user):
        return [
            DocumentResponse(
                user_id=str(consented_user.user_id),
                created_at="2021-01-01",
                document_type=DocumentType.APPEAL_ACKNOWLEDGMENT.document_type_description,
                fineos_document_id="3011",
                name="test.pdf",
                description="Mock File",
                is_legal_notice=False,
                pfml_document_type=None,
            ),
            DocumentResponse(
                user_id=str(consented_user.user_id),
                created_at="2021-01-01",
                document_type=DocumentType.CHANGE_REQUEST_APPROVED.document_type_description,
                fineos_document_id="2011",
                name="test.pdf",
                description="Mock File",
                is_legal_notice=True,
                pfml_document_type=None,
            ),
        ]

    @mock.patch(
        "massgov.pfml.services.documents.get_document_service.GetDocumentService.get_user_documents"
    )
    def test_success(
        self,
        mock_get_user_documents,
        documents,
        user_with_submitted_application,
        client,
        consented_user_token,
    ):
        mock_get_user_documents.return_value = documents
        response = client.get(
            "/v1/users/{}/documents".format(user_with_submitted_application.user_id),
            headers={"Authorization": f"Bearer {consented_user_token}"},
        )

        assert response.status_code == 200

    @mock.patch(
        "massgov.pfml.services.documents.get_document_service.GetDocumentService.get_user_documents"
    )
    def test_success_with_query_params(
        self,
        mock_get_user_documents,
        documents,
        user_with_submitted_application,
        client,
        consented_user_token,
    ):
        querystring = urlencode(
            {"document_type": DocumentType.APPEAL_ACKNOWLEDGMENT.document_type_description}
        )
        mock_get_user_documents.return_value = documents

        response = client.get(
            "/v1/users/{}/documents?{}".format(
                user_with_submitted_application.user_id, querystring
            ),
            headers={"Authorization": f"Bearer {consented_user_token}"},
        )

        assert response.status_code == 200

    @mock.patch(
        "massgov.pfml.services.documents.get_document_service.GetDocumentService.get_user_documents"
    )
    def test_returns_empty_list_when_no_submitted_apps(
        self,
        mock_get_user_documents,
        user,
        client,
        auth_token,
    ):
        querystring = urlencode(
            {"document_type": DocumentType.APPEAL_ACKNOWLEDGMENT.document_type_description}
        )
        ApplicationFactory.create(user=user)

        response = client.get(
            "/v1/users/{}/documents?{}".format(user.user_id, querystring),
            headers={"Authorization": f"Bearer {auth_token}"},
        )

        mock_get_user_documents.assert_not_called()

        assert response.status_code == 200
        response_body = response.json()
        assert response_body.get("data") == []

    @mock.patch(
        "massgov.pfml.services.documents.get_document_service.GetDocumentService.get_user_documents"
    )
    def test_failure_with_query_params(
        self,
        mock_get_user_documents,
        documents,
        user_with_submitted_application,
        client,
        consented_user_token,
    ):
        querystring = urlencode({"document_type": "fail"})
        mock_get_user_documents.return_value = documents

        response = client.get(
            "/v1/users/{}/documents?{}".format(
                user_with_submitted_application.user_id, querystring
            ),
            headers={"Authorization": f"Bearer {consented_user_token}"},
        )

        assert response.status_code == 400

    @mock.patch(
        "massgov.pfml.services.documents.get_document_service.GetDocumentService.get_user_documents"
    )
    def test_fineos_failure(
        self,
        mock_get_user_documents,
        user_with_submitted_application,
        client,
        consented_user_token,
    ):
        mock_get_user_documents.side_effect = FINEOSClientError()
        response = client.get(
            "/v1/users/{}/documents".format(user_with_submitted_application.user_id),
            headers={"Authorization": f"Bearer {consented_user_token}"},
        )

        assert response.status_code == 400

    class Test1099DocumentsGet:
        @pytest.fixture(autouse=True)
        def irs_1099_documents(self, consented_user):
            return [
                DocumentResponse(
                    user_id=str(consented_user.user_id),
                    created_at="2021-01-01",
                    document_type=DocumentType.IRS_1099G_TAX_FORM_FOR_CLAIMANTS.document_type_description,
                    fineos_document_id="3011",
                    name="test.pdf",
                    description="Mock File",
                    is_legal_notice=False,
                    pfml_document_type=None,
                )
            ]

        @mock.patch(
            "massgov.pfml.services.documents.get_document_service.GetDocumentService.get_user_documents"
        )
        def test_auth_failure_user_has_multiple_tax_identifiers(
            self,
            mock_get_user_documents,
            irs_1099_documents,
            user_with_submitted_application,
            client,
            consented_user_token,
            test_db_session,
        ):
            mock_get_user_documents.return_value = irs_1099_documents
            employee1 = EmployeeFactory.create()
            employee2 = EmployeeFactory.create()
            ApplicationFactory.create(
                user=user_with_submitted_application,
                submitted_time=date.today(),
                tax_identifier=employee1.tax_identifier,
            )
            ApplicationFactory.create(
                user=user_with_submitted_application,
                submitted_time=date.today(),
                tax_identifier=employee2.tax_identifier,
            )
            test_db_session.commit()

            querystring = urlencode(
                {
                    "document_type": DocumentType.IRS_1099G_TAX_FORM_FOR_CLAIMANTS.document_type_description
                }
            )
            response = client.get(
                "/v1/users/{}/documents?{}".format(
                    user_with_submitted_application.user_id, querystring
                ),
                headers={"Authorization": f"Bearer {consented_user_token}"},
            )

            assert response.status_code == 403


def document_data(extension="pdf", contentType=None):
    return Base64EncodedFileData(
        base64EncodedFileContents="Zm9v",
        fileExtension=extension,
        contentType=contentType,
        fileName=DocumentType.IRS_1099G_TAX_FORM_FOR_CLAIMANTS.document_type_description,
        fileSizeInBytes=10,
    )


class TestDocumentDownload:
    @mock.patch("massgov.pfml.api.users.download_customer_document")
    @pytest.mark.parametrize(
        "extension,contentType",
        (
            ("pdf", "application/pdf"),
            ("jpeg", "image/jpeg,image/pjpeg"),
            ("jpeg", "image/jpeg"),
            ("png", "image/png"),
            ("pdf", "application/octet-stream"),
        ),
    )
    def test_success(
        self,
        mock_download_customer_document,
        client,
        consented_user,
        consented_user_token,
        extension,
        contentType,
    ):
        mock_download_customer_document.return_value = document_data(
            extension, contentType if contentType != "application/octet-stream" else None
        )
        fineos_document_id = "mock"
        response = client.get(
            "/v1/users/{}/documents/{}".format(consented_user.user_id, fineos_document_id),
            headers={"Authorization": f"Bearer {consented_user_token}"},
        )

        assert response.status_code == 200
        assert response.headers["content-type"] == contentType

    @mock.patch("massgov.pfml.api.users.download_customer_document")
    def test_fineos_failure(
        self, mock_download_customer_document, client, consented_user, consented_user_token
    ):
        mock_download_customer_document.side_effect = FINEOSClientError()
        fineos_document_id = "mock"
        response = client.get(
            "/v1/users/{}/documents/{}".format(consented_user.user_id, fineos_document_id),
            headers={"Authorization": f"Bearer {consented_user_token}"},
        )

        assert response.status_code == 400

    class Test1099DocumentDownload:
        @pytest.fixture(autouse=True)
        def user_with_documents(self, consented_user):
            consented_user.consented_to_view_tax_documents = True
            return consented_user

        @mock.patch("massgov.pfml.api.users.download_customer_document")
        def test_auth_failure_user_has_multiple_tax_identifiers(
            self,
            mock_download_customer_document,
            user_with_documents,
            client,
            consented_user_token,
            test_db_session,
        ):
            mock_download_customer_document.return_value = document_data()
            employee1 = EmployeeFactory.create()
            employee2 = EmployeeFactory.create()
            ApplicationFactory.create(
                user=user_with_documents,
                submitted_time=date.today(),
                tax_identifier=employee1.tax_identifier,
            )
            ApplicationFactory.create(
                user=user_with_documents,
                submitted_time=date.today(),
                tax_identifier=employee2.tax_identifier,
            )

            test_db_session.commit()

            fineos_document_id = "mock"
            response = client.get(
                "/v1/users/{}/documents/{}".format(user_with_documents.user_id, fineos_document_id),
                headers={"Authorization": f"Bearer {consented_user_token}"},
            )

            assert response.status_code == 403

        @mock.patch("massgov.pfml.api.users.download_customer_document")
        def test_auth_failure_user_has_not_consented(
            self,
            mock_download_customer_document,
            user_with_documents,
            client,
            consented_user_token,
            test_db_session,
        ):
            mock_download_customer_document.return_value = document_data()
            user_with_documents.consented_to_view_tax_documents = False
            test_db_session.commit()
            fineos_document_id = "mock"

            response = client.get(
                "/v1/users/{}/documents/{}".format(user_with_documents.user_id, fineos_document_id),
                headers={"Authorization": f"Bearer {consented_user_token}"},
            )

            assert response.status_code == 403


def test_post_user_profile_check_for_updates_unauthorized_user(
    client, test_db_session, consented_user, consented_user_token
):
    user_2 = UserFactory.create()
    application = ApplicationFactory.create(user=user_2, submitted_time=datetime.now())
    test_db_session.commit()

    body = {
        "from_application": application.application_id,
    }

    response = client.post(
        "/v1/users/{}/profile/check-for-updates".format(consented_user.user_id),
        headers={"Authorization": f"Bearer {consented_user_token}"},
        json=apply_custom_encoder(body),
    )
    assert response.status_code == 403


def test_post_user_profile_check_for_updates_unauthorized_application(
    client, test_db_session, consented_user, consented_user_token
):
    ApplicationFactory.create(user=consented_user, submitted_time=datetime.now())
    application2 = ApplicationFactory.create(submitted_time=datetime.now())

    test_db_session.commit()

    body = {
        "from_application": application2.application_id,
    }

    response = client.post(
        "/v1/users/{}/profile/check-for-updates".format(consented_user.user_id),
        headers={"Authorization": f"Bearer {consented_user_token}"},
        json=apply_custom_encoder(body),
    )
    assert response.status_code == 403


def test_post_user_profile_check_for_updates(
    client, test_db_session, consented_user, consented_user_token, application
):
    application = ApplicationFactory.create(user=consented_user, submitted_time=datetime.now())
    test_db_session.commit()

    body = {
        "from_application": application.application_id,
    }

    response = client.post(
        "/v1/users/{}/profile/check-for-updates".format(consented_user.user_id),
        headers={"Authorization": f"Bearer {consented_user_token}"},
        json=apply_custom_encoder(body),
    )

    assert response.status_code == 200
    response_body = response.json()
    assert response_body.get("data")["profile_updates"]["gender"]["new_value"] is None
    assert response_body.get("data")["profile_updates"]["gender"]["old_value"] == "NOT_LISTED"
    assert response_body.get("data")["profile_updates"]["raceEthnicity"]["new_value"] == []
    assert response_body.get("data")["profile_updates"]["raceEthnicity"]["old_value"] == [
        "ASIAN",
        "HISPANIC_OR_LATINO",
    ]
