import uuid
from datetime import datetime
from unittest import mock

import pytest

from massgov.pfml.api.models.documents.common import ContentType as AllowedContentTypes
from massgov.pfml.api.models.documents.responses import DocumentResponse
from massgov.pfml.db.models.documents import Document, LkDocumentType


@pytest.fixture
def mock_db_document():
    mock_db_document = mock.MagicMock(spec=Document)
    mock_db_document.document_type_instance = None
    mock_db_document.document_id = 1
    mock_db_document.user_id = uuid.uuid4()
    mock_db_document.application_id = uuid.uuid4()
    mock_db_document.appeal_id = uuid.uuid4()
    mock_db_document.document_type_id = 1
    mock_db_document.pfml_document_type_id = 1
    mock_db_document.fineos_id = 3131
    mock_db_document.size_bytes = 47029
    mock_db_document.is_stored_in_s3 = True
    mock_db_document.name = "Mock Passport"
    mock_db_document.description = "Page 1"
    mock_db_document.created_at = datetime.now()
    mock_db_document.document_type_instance = LkDocumentType(
        document_type_id=1, document_type_description="Passport"
    )
    return mock_db_document


def test_document_response_from_orm(mock_db_document):

    # GIVEN
    valid_content_type = AllowedContentTypes.pdf

    # WHEN
    document_response = DocumentResponse.from_orm(mock_db_document, valid_content_type)

    # THEN
    assert document_response.is_legal_notice is False

    assert document_response.user_id == mock_db_document.user_id
    assert document_response.application_id == mock_db_document.application_id
    assert document_response.appeal_id == mock_db_document.appeal_id
    assert (
        document_response.created_at == mock_db_document.created_at.date()
    )  # DocumentResponse uses type date
    assert (
        document_response.document_type
        == mock_db_document.document_type_instance.document_type_description
    )
    assert document_response.appeal_id == mock_db_document.appeal_id
    assert document_response.content_type == valid_content_type
    assert document_response.appeal_id == mock_db_document.appeal_id
    assert document_response.fineos_document_id == str(mock_db_document.fineos_id)
    assert document_response.name == mock_db_document.name
    assert document_response.description == mock_db_document.description
    assert (
        document_response.pfml_document_type
        == mock_db_document.document_type_instance.document_type_description
    )
