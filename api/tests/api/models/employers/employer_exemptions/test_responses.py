from massgov.pfml.api.models.employer_exemptions.responses import (
    EmployerExemptionApplicationResponseBody,
)
from massgov.pfml.db.models.factories import (
    EmployerExemptionApplicationDraftFactory,
    EmployerExemptionApplicationDraftPrivatePlanFactory,
)


def test_purchased_plan_insurance_provider_with_insurance_plan(initialize_factories_session):
    employer_exemption_application = EmployerExemptionApplicationDraftPrivatePlanFactory.create()
    response_body = EmployerExemptionApplicationResponseBody.from_orm(
        employer_exemption_application
    ).dict()

    purchased_plan = response_body["purchased_plan"]

    assert response_body["is_self_insured_plan"] is False
    assert purchased_plan["insurance_provider_id"] == 1
    assert purchased_plan["insurance_provider_name"] == "American Fidelity Assurance Company"
    assert purchased_plan["insurance_plan_id"] == 1
    assert purchased_plan["insurance_plan_name"] == "G1500"


def test_purchased_plan_insurance_provider_without_insurance_plan(initialize_factories_session):
    employer_exemption_application = EmployerExemptionApplicationDraftFactory.create(
        is_self_insured_plan=False, insurance_provider_id=1
    )

    response_body = EmployerExemptionApplicationResponseBody.from_orm(
        employer_exemption_application
    ).dict()

    purchased_plan = response_body["purchased_plan"]

    assert response_body["is_self_insured_plan"] is False
    assert purchased_plan["insurance_provider_id"] == 1
    assert purchased_plan["insurance_provider_name"] == "American Fidelity Assurance Company"
    assert purchased_plan["insurance_plan_id"] is None
    assert purchased_plan["insurance_plan_name"] is None
