from datetime import date, datetime, timedelta
from unittest import mock

import pytest

from massgov.pfml.api.constants.documents import ABSENCE_REASON_TO_DOCUMENT_TYPE_MAPPING
from massgov.pfml.api.models.claims.responses import DocumentRequirement
from massgov.pfml.api.models.documents.common import DocumentType
from massgov.pfml.api.models.documents.responses import DocumentResponse
from massgov.pfml.db.lookup_data.absences import AbsenceReason
from massgov.pfml.db.lookup_data.applications import LeaveReason
from massgov.pfml.db.models.factories import AbsencePeriodFactory
from massgov.pfml.fineos.models.customer_api import OutstandingSupportingEvidence
from massgov.pfml.services.documents.required_documents import DocumentRequirementService


@pytest.fixture(autouse=True)
def setup_factories(initialize_factories_session):
    return


@pytest.fixture
def service(test_db_session):
    # Create an instance of the service with the mocked test_db_session
    return DocumentRequirementService(test_db_session)


@pytest.fixture
def claim_with_medical_leave_period(application_with_claim):
    claim = application_with_claim.claim
    claim.absence_periods = [
        AbsencePeriodFactory.create(
            absence_reason_id=AbsenceReason.SERIOUS_HEALTH_CONDITION_EMPLOYEE.absence_reason_id,
            absence_period_start_date=date.today() + timedelta(days=5),
            absence_period_end_date=date.today() + timedelta(days=20),
        )
    ]
    return claim


@pytest.fixture
def claim_with_no_absence_periods(application_with_claim):
    claim = application_with_claim.claim
    claim.absence_periods = []
    return claim


def test_are_all_required_documents_received_with_no_claim(service, application):
    # Test when the application has no claim
    all_required_documents_received = service.are_all_required_documents_received(application)
    assert all_required_documents_received is False, "Expected False when application has no claim"


def test_are_all_required_documents_received_with_unreceived_documents(
    service, application_with_claim
):
    # Mock the service method to return unreceived document types
    service.get_unreceived_required_document_types = mock.MagicMock(
        return_value=[DocumentType.own_serious_health_condition_form]
    )

    # Test when there are unreceived documents
    all_required_documents_received = service.are_all_required_documents_received(
        application_with_claim
    )
    assert (
        all_required_documents_received is False
    ), "Expected False when there are unreceived documents"


def test_are_all_required_documents_received_with_all_documents_received(
    service, application_with_claim
):
    # Mock the service method to return an empty list (all documents received)
    service.get_unreceived_required_document_types = mock.MagicMock(return_value=[])

    # Test when all documents are received
    all_required_documents_received = service.are_all_required_documents_received(
        application_with_claim
    )
    assert (
        all_required_documents_received is True
    ), "Expected True when all required documents are received"


@mock.patch("massgov.pfml.fineos.mock_client.MockFINEOSClient.get_outstanding_evidence")
def test_get_unreceived_required_document_types_when_application_has_received_all_required_documents(
    mock_get_outstanding_evidence, service, application_with_claim
):
    mock_get_outstanding_evidence.return_value = [
        OutstandingSupportingEvidence.parse_obj(
            {
                "docReceived": True,
                "name": DocumentType.identification_proof,
                "rootCaseId": "NTN-#",
                "source": "John Doe",
                "uploadCaseNumber": application_with_claim.fineos_absence_id,
            }
        ),
        OutstandingSupportingEvidence.parse_obj(
            {
                "docReceived": True,
                "name": DocumentType.own_serious_health_condition_form,
                "rootCaseId": "NTN-#",
                "source": "John Doe",
                "uploadCaseNumber": application_with_claim.fineos_absence_id,
            }
        ),
    ]
    required_document_types = [
        DocumentType.own_serious_health_condition_form,
        DocumentType.identification_proof,
    ]
    service.get_required_document_types_for_application = mock.MagicMock(
        return_value=required_document_types
    )

    document_types = service.get_unreceived_required_document_types(application_with_claim)

    # Assert the result
    assert document_types == [], "Expected no unreceived documents"


@mock.patch("massgov.pfml.fineos.mock_client.MockFINEOSClient.get_outstanding_evidence")
def test_get_unreceived_required_document_types_when_application_has_no_outstanding_evidence(
    mock_get_outstanding_evidence, service, application_with_claim
):
    # Not appearing in the response means the evidence is either satisfied or not required.
    mock_get_outstanding_evidence.return_value = []
    required_document_types = [
        DocumentType.own_serious_health_condition_form,
        DocumentType.identification_proof,
    ]
    service.get_required_document_types_for_application = mock.MagicMock(
        return_value=required_document_types
    )

    document_types = service.get_unreceived_required_document_types(application_with_claim)

    # Assert the result
    assert document_types == [], "No outstanding evidence, so no unreceived required documents"


@mock.patch("massgov.pfml.fineos.mock_client.MockFINEOSClient.get_outstanding_evidence")
def test_get_unreceived_required_document_types_when_employer_response_is_outstanding_with_no_documents(
    mock_get_outstanding_evidence, service, application_with_claim
):
    mock_get_outstanding_evidence.return_value = [
        OutstandingSupportingEvidence.parse_obj(
            {
                "docReceived": False,
                "name": "Employer Confirmation of Leave Data",
                "rootCaseId": "NTN-#",
                "source": "Acme Corp.",
                "uploadCaseNumber": application_with_claim.fineos_absence_id,
            }
        ),
    ]

    required_document_types = [
        DocumentType.own_serious_health_condition_form,
        DocumentType.identification_proof,
    ]
    service.get_required_document_types_for_application = mock.MagicMock(
        return_value=required_document_types
    )

    document_types = service.get_unreceived_required_document_types(application_with_claim)

    # Assert the result
    assert (
        document_types == []
    ), "No RELEVANT outstanding evidence, so no unreceived required documents"


@mock.patch("massgov.pfml.fineos.mock_client.MockFINEOSClient.get_outstanding_evidence")
def test_get_unreceived_required_document_types_when_application_has_an_unreceived_required_document(
    mock_get_outstanding_evidence, service, application_with_claim
):
    mock_get_outstanding_evidence.return_value = [
        OutstandingSupportingEvidence.parse_obj(
            {
                "docReceived": False,
                "name": DocumentType.identification_proof,
                "rootCaseId": "NTN-#",
                "source": "John Doe",
                "uploadCaseNumber": application_with_claim.fineos_absence_id,
            }
        ),
        OutstandingSupportingEvidence.parse_obj(
            {
                "docReceived": True,
                "name": DocumentType.own_serious_health_condition_form,
                "rootCaseId": "NTN-#",
                "source": "John Doe",
                "uploadCaseNumber": application_with_claim.fineos_absence_id,
            }
        ),
    ]

    required_document_types = [
        DocumentType.own_serious_health_condition_form,
        DocumentType.identification_proof,
    ]
    service.get_required_document_types_for_application = mock.MagicMock(
        return_value=required_document_types
    )

    document_types = service.get_unreceived_required_document_types(application_with_claim)

    # Assert the result
    assert document_types == [
        DocumentType.identification_proof
    ], "identification_proof is outstanding evidence"


def test_get_unreceived_required_document_types_when_application_has_no_claim(
    service,
    application,
):
    with pytest.raises(
        ValueError,
        match=f"Cannot get unreceived required document types for application {application.application_id}. The application does not have a claim.",
    ):
        service.get_unreceived_required_document_types(application)


def test_get_document_requirements_for_claim_via_absence_periods_with_no_absence_periods(
    service,
    claim_with_no_absence_periods,
):

    with pytest.raises(
        ValueError,
        match=f"Cannot get document requirements for claim {claim_with_no_absence_periods.claim_id}. The claim must have associated absence periods.",
    ):
        service.get_document_requirements_for_claim_via_absence_periods(
            claim_with_no_absence_periods
        )


def test_get_document_requirements_for_claim_via_absence_periods_with_no_document_type_requirements(
    service,
    claim_with_medical_leave_period,
):

    service.get_required_document_types_for_absence_periods = mock.MagicMock(return_value=[])

    document_requirements = service.get_document_requirements_for_claim_via_absence_periods(
        claim_with_medical_leave_period
    )

    assert (
        document_requirements == []
    ), "Expected no document requirements when there are no document type requirements"


def test_get_document_requirements_for_application_with_no_document_type_requirements(
    service,
    application_with_claim,
):

    service.get_required_document_types_for_application = mock.MagicMock(return_value=[])

    document_requirements = service.get_document_requirements_for_application(
        application_with_claim
    )

    assert (
        document_requirements == []
    ), "Expected no document requirements when there are no document type requirements"


def test_determine_document_requirements_when_one_of_each_document_uploaded(service):
    document_type_requirements = [
        DocumentType.own_serious_health_condition_form,
        DocumentType.identification_proof,
    ]
    uploaded_documents = [
        DocumentResponse(
            name="Identification Proof",
            description=DocumentType.identification_proof,
            document_type=DocumentType.identification_proof,
            created_at=datetime(2020, 1, 1),
            is_legal_notice=False,
            pfml_document_type=DocumentType.identification_proof,
        ),
        DocumentResponse(
            name="Own serious health condition form",
            description=DocumentType.own_serious_health_condition_form,
            document_type=DocumentType.own_serious_health_condition_form,
            created_at=datetime(2020, 2, 1),
            is_legal_notice=False,
            pfml_document_type=DocumentType.own_serious_health_condition_form,
        ),
    ]

    document_requirements = service.determine_document_requirements(
        document_type_requirements, uploaded_documents
    )

    assert len(document_requirements) == 2, "Expected two document requirements"
    assert (
        DocumentRequirement(
            document_type=DocumentType.identification_proof, upload_date=datetime(2020, 1, 1)
        )
        in document_requirements
    )
    assert (
        DocumentRequirement(
            document_type=DocumentType.own_serious_health_condition_form,
            upload_date=datetime(2020, 2, 1),
        )
        in document_requirements
    )


def test_determine_document_requirements_when_two_identification_documents_uploaded(service):
    document_type_requirements = [
        DocumentType.own_serious_health_condition_form,
        DocumentType.identification_proof,
    ]
    uploaded_documents = [
        DocumentResponse(
            name="Identification Proof 1",
            description=DocumentType.identification_proof,
            document_type=DocumentType.identification_proof,
            created_at=datetime(2020, 1, 1),
            is_legal_notice=False,
            pfml_document_type=DocumentType.identification_proof,
        ),
        DocumentResponse(
            name="Identification Proof 2",
            description=DocumentType.identification_proof,
            document_type=DocumentType.identification_proof,
            created_at=datetime(2020, 5, 5),
            is_legal_notice=False,
            pfml_document_type=DocumentType.identification_proof,
        ),
    ]

    document_requirements = service.determine_document_requirements(
        document_type_requirements, uploaded_documents
    )

    assert len(document_requirements) == 2, "Expected two document requirements"
    assert (
        DocumentRequirement(
            document_type=DocumentType.identification_proof, upload_date=datetime(2020, 5, 5)
        )
        in document_requirements
    )
    assert (
        DocumentRequirement(
            document_type=DocumentType.own_serious_health_condition_form,
            upload_date=None,
        )
        in document_requirements
    )


def test_determine_document_requirements_when_no_documents_uploaded(service):
    document_type_requirements = [
        DocumentType.own_serious_health_condition_form,
        DocumentType.identification_proof,
    ]
    uploaded_documents = []

    document_requirements = service.determine_document_requirements(
        document_type_requirements, uploaded_documents
    )

    assert len(document_requirements) == 2, "Expected two document requirements"
    assert (
        DocumentRequirement(document_type=DocumentType.identification_proof, upload_date=None)
        in document_requirements
    )
    assert (
        DocumentRequirement(
            document_type=DocumentType.own_serious_health_condition_form,
            upload_date=None,
        )
        in document_requirements
    )


def test_get_required_document_types_for_application_for_serious_health_condition(
    service, application
):

    document_types = service.get_required_document_types_for_application(application)

    assert len(document_types) == 2, "Expected two required document types"
    expected_document_types = ABSENCE_REASON_TO_DOCUMENT_TYPE_MAPPING.get(
        LeaveReason.SERIOUS_HEALTH_CONDITION_EMPLOYEE.leave_reason_description
    )
    for doc_type in expected_document_types:
        assert doc_type in document_types, f"Expected {doc_type} to be in document types"


def test_get_required_document_types_for_application_for_child_bonding(service, application):

    application.leave_reason_id = LeaveReason.CHILD_BONDING.leave_reason_id

    document_types = service.get_required_document_types_for_application(application)

    assert len(document_types) == 2, "Expected two required document types"
    expected_document_types = ABSENCE_REASON_TO_DOCUMENT_TYPE_MAPPING.get(
        LeaveReason.CHILD_BONDING.leave_reason_description
    )
    for doc_type in expected_document_types:
        assert doc_type in document_types, f"Expected {doc_type} to be in document types"


def test_get_required_document_types_for_application_for_care_of_family_member(
    service, application
):

    application.leave_reason_id = LeaveReason.CARE_FOR_A_FAMILY_MEMBER.leave_reason_id

    document_types = service.get_required_document_types_for_application(application)

    assert len(document_types) == 2, "Expected two required document types"
    expected_document_types = ABSENCE_REASON_TO_DOCUMENT_TYPE_MAPPING.get(
        LeaveReason.CARE_FOR_A_FAMILY_MEMBER.leave_reason_description
    )
    for doc_type in expected_document_types:
        assert doc_type in document_types, f"Expected {doc_type} to be in document types"


def test_get_required_document_types_for_application_for_pregnancy_maternity(service, application):

    application.leave_reason_id = LeaveReason.PREGNANCY_MATERNITY.leave_reason_id

    document_types = service.get_required_document_types_for_application(application)

    assert len(document_types) == 2, "Expected two required document types"
    expected_document_types = ABSENCE_REASON_TO_DOCUMENT_TYPE_MAPPING.get(
        LeaveReason.PREGNANCY_MATERNITY.leave_reason_description
    )
    for doc_type in expected_document_types:
        assert doc_type in document_types, f"Expected {doc_type} to be in document types"


def test_get_required_document_types_for_application_for_invalid_leave_reason(service, application):

    application.leave_reason_id = 999

    document_types = service.get_required_document_types_for_application(application)

    assert document_types == [], "No mapping for invalid leave reason, so expect an empty list"


def test_get_required_document_types_for_absence_periods_for_serious_health_condition(service):

    absence_periods = [
        AbsencePeriodFactory.create(
            absence_reason_id=AbsenceReason.SERIOUS_HEALTH_CONDITION_EMPLOYEE.absence_reason_id,
            absence_period_start_date=date.today() + timedelta(days=5),
            absence_period_end_date=date.today() + timedelta(days=20),
        )
    ]

    document_types = service.get_required_document_types_for_absence_periods(absence_periods)

    assert len(document_types) == 2, "Expected two required document types"
    expected_document_types = ABSENCE_REASON_TO_DOCUMENT_TYPE_MAPPING.get(
        AbsenceReason.SERIOUS_HEALTH_CONDITION_EMPLOYEE.absence_reason_description
    )
    for doc_type in expected_document_types:
        assert doc_type in document_types, f"Expected {doc_type} to be in document types"


def test_get_required_document_types_for_absence_periods_for_child_bonding(service):

    absence_periods = [
        AbsencePeriodFactory.create(
            absence_reason_id=AbsenceReason.CHILD_BONDING.absence_reason_id,
            absence_period_start_date=date.today() + timedelta(days=5),
            absence_period_end_date=date.today() + timedelta(days=20),
        )
    ]

    document_types = service.get_required_document_types_for_absence_periods(absence_periods)

    assert len(document_types) == 2, "Expected two required document types"
    expected_document_types = ABSENCE_REASON_TO_DOCUMENT_TYPE_MAPPING.get(
        AbsenceReason.CHILD_BONDING.absence_reason_description
    )
    for doc_type in expected_document_types:
        assert doc_type in document_types, f"Expected {doc_type} to be in document types"


def test_get_required_document_types_for_absence_periods_for_care_of_family_member(service):

    absence_periods = [
        AbsencePeriodFactory.create(
            absence_reason_id=AbsenceReason.CARE_OF_A_FAMILY_MEMBER.absence_reason_id,
            absence_period_start_date=date.today() + timedelta(days=5),
            absence_period_end_date=date.today() + timedelta(days=20),
        )
    ]

    document_types = service.get_required_document_types_for_absence_periods(absence_periods)

    assert len(document_types) == 2, "Expected two required document types"
    expected_document_types = ABSENCE_REASON_TO_DOCUMENT_TYPE_MAPPING.get(
        AbsenceReason.CARE_OF_A_FAMILY_MEMBER.absence_reason_description
    )
    for doc_type in expected_document_types:
        assert doc_type in document_types, f"Expected {doc_type} to be in document types"


def test_get_required_document_types_for_absence_periods_for_pregnancy_maternity(service):

    absence_periods = [
        AbsencePeriodFactory.create(
            absence_reason_id=AbsenceReason.PREGNANCY_MATERNITY.absence_reason_id,
            absence_period_start_date=date.today() + timedelta(days=5),
            absence_period_end_date=date.today() + timedelta(days=20),
        )
    ]

    document_types = service.get_required_document_types_for_absence_periods(absence_periods)

    assert len(document_types) == 2, "Expected two required document types"
    expected_document_types = ABSENCE_REASON_TO_DOCUMENT_TYPE_MAPPING.get(
        AbsenceReason.PREGNANCY_MATERNITY.absence_reason_description
    )
    for doc_type in expected_document_types:
        assert doc_type in document_types, f"Expected {doc_type} to be in document types"


def test_get_required_document_types_for_absence_periods_for_med_preg(service):

    absence_periods = [
        AbsencePeriodFactory.create(
            absence_reason_id=AbsenceReason.PREGNANCY_MATERNITY.absence_reason_id,
            absence_period_start_date=date.today() + timedelta(days=5),
            absence_period_end_date=date.today() + timedelta(days=20),
        ),
        AbsencePeriodFactory.create(
            absence_reason_id=AbsenceReason.SERIOUS_HEALTH_CONDITION_EMPLOYEE.absence_reason_id,
            absence_period_start_date=date.today() + timedelta(days=5),
            absence_period_end_date=date.today() + timedelta(days=20),
        ),
    ]

    document_types = service.get_required_document_types_for_absence_periods(absence_periods)

    assert len(document_types) == 3
    assert "Identification Proof" in document_types
    assert "Pregnancy/Maternity form" in document_types
    assert "Own serious health condition form" in document_types


def test_get_required_document_types_for_absence_periods_for_no_absence_periods(service):

    document_types = service.get_required_document_types_for_absence_periods([])

    assert document_types == [], "No absence periods, so expect an empty list"


def test_get_required_document_types_by_absence_reason_valid_reason(service):
    absence_reason = AbsenceReason.CHILD_BONDING.absence_reason_description

    document_types = service.get_required_document_types_by_absence_reason(absence_reason)

    assert len(document_types) == 2, "Expected two required document types"

    expected_document_types = ABSENCE_REASON_TO_DOCUMENT_TYPE_MAPPING.get(
        AbsenceReason.CHILD_BONDING.absence_reason_description
    )
    for doc_type in expected_document_types:
        assert doc_type in document_types, f"Expected {doc_type} to be in document types"


def test_get_required_document_types_by_absence_reason_invalid_reason(service):
    absence_reason = "INVALID"

    document_types = service.get_required_document_types_by_absence_reason(absence_reason)

    assert document_types == [], "Expected an empty list for invalid absence reason"


def test_get_required_document_types_by_absence_reason_no_reason(service):
    absence_reason = None

    document_types = service.get_required_document_types_by_absence_reason(absence_reason)

    assert document_types == [], "Expected an empty list for missing absence reason"
