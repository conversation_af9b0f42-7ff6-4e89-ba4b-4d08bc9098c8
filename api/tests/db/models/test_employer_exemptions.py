from sqlalchemy import inspect

from massgov.pfml.db.models.employer_exemptions import EmployerExemptionApplication
from massgov.pfml.db.models.factories import (
    EmployerExemptionApplicationDraftPrivatePlanFactory,
    EmployerExemptionApplicationFactory,
)
from massgov.pfml.db.models.phone import Phone


def test_is_db_column_name():
    for column in EmployerExemptionApplication.columns().keys():
        assert EmployerExemptionApplication.is_db_column_name(column) is True

    for relationship in inspect(EmployerExemptionApplication).relationships.keys():
        assert EmployerExemptionApplication.is_db_column_name(relationship) is False


def test_created_by_user_relationship(initialize_factories_session):
    employer_exemption_application = EmployerExemptionApplicationDraftPrivatePlanFactory.create()
    assert employer_exemption_application.created_by_user is not None
    assert employer_exemption_application.created_by_user.user_id is not None
    assert employer_exemption_application.created_by_user.email_address is not None


def test_insurance_provider_relationship(initialize_factories_session):
    employer_exemption_application = EmployerExemptionApplicationDraftPrivatePlanFactory.create()

    assert employer_exemption_application.insurance_provider_id == 1
    assert employer_exemption_application.insurance_provider is not None
    assert (
        employer_exemption_application.insurance_provider.insurance_provider_name
        == "American Fidelity Assurance Company"
    )


def test_insurance_plan_relationship(initialize_factories_session):
    employer_exemption_application = EmployerExemptionApplicationDraftPrivatePlanFactory.create()

    assert employer_exemption_application.insurance_plan_id == 1
    assert employer_exemption_application.insurance_plan is not None
    assert employer_exemption_application.insurance_plan.form_name == "G1500"


def test_get_columns_to_clear_when_is_self_insured():
    expected_col = ["insurance_provider_id", "insurance_plan_id"]

    col = EmployerExemptionApplication.get_columns_to_clear_when_is_self_insured()
    assert sorted(col) == sorted(expected_col)


def test_get_columns_to_clear_when_is_self_insured_family():
    expected_col = [
        "does_plan_provide_enough_medical_leave",  # medical only question
    ]

    col = EmployerExemptionApplication.get_columns_to_clear_when_is_self_insured_family()
    assert sorted(col) == sorted(expected_col)


def test_get_columns_to_clear_when_is_self_insured_medical(initialize_factories_session):
    expected_col = [
        "does_plan_provide_enough_armed_forces_illness_leave",  # family only question
        "does_plan_provide_enough_armed_forces_leave",  # family only question
        "does_plan_provide_enough_bonding_leave",  # family only question
        "does_plan_provide_enough_caring_leave",  # family only question
        "does_plan_provide_intermittent_armed_forces_leave",  # family only question
        "does_plan_provide_intermittent_bonding_leave",  # family only question
        "does_plan_provide_intermittent_caring_leave",  # family only question
    ]

    col = EmployerExemptionApplication.get_columns_to_clear_when_is_self_insured_medical()
    assert sorted(col) == sorted(expected_col)


def test_get_columns_to_clear_when_is_not_self_insured():
    expected_col = [
        "are_employer_withholdings_within_allowable_amount",
        "does_employer_withhold_premiums",
        "does_plan_cover_all_employees",
        "does_plan_cover_employee_contribution",
        "does_plan_cover_former_employees",
        "does_plan_favor_paid_leave_benefits",
        "does_plan_pay_enough_benefits",
        "does_plan_provide_enough_armed_forces_illness_leave",
        "does_plan_provide_enough_armed_forces_leave",
        "does_plan_provide_enough_bonding_leave",
        "does_plan_provide_enough_caring_leave",
        "does_plan_provide_enough_leave",
        "does_plan_provide_enough_medical_leave",
        "does_plan_provide_intermittent_armed_forces_leave",
        "does_plan_provide_intermittent_bonding_leave",
        "does_plan_provide_intermittent_caring_leave",
        "does_plan_provide_intermittent_medical_leave",
        "does_plan_provide_pfml_job_protection",
        "does_plan_provide_return_to_work_benefits",
        "has_obtained_surety_bond",
        "insurance_plan_expires_at",
        "surety_bond_amount",
        "surety_company",
    ]

    col = EmployerExemptionApplication.get_columns_to_clear_when_is_not_self_insured()
    assert sorted(col) == sorted(expected_col)


def test_get_columns_to_clear_when_not_third_party_admin():
    expected_col = [
        "tpa_business_name",
        "tpa_contact_title",
        "tpa_contact_first_name",
        "tpa_contact_last_name",
        "tpa_contact_phone_id",
        "tpa_contact_email_address",
    ]

    col = EmployerExemptionApplication.get_columns_to_clear_when_not_third_party_admin()
    assert sorted(col) == sorted(expected_col)


def test_is_deprecated():
    expected_col = []

    for db_column_name in EmployerExemptionApplication.columns().keys():
        db_column_in_expected_list = db_column_name in expected_col
        assert db_column_in_expected_list == EmployerExemptionApplication.is_deprecated(
            db_column_name
        ), f"{db_column_name} expected {db_column_in_expected_list}, received {not db_column_in_expected_list}"


def test_is_phone_db_model_foreign_key():
    expected_col = ["contact_phone_id", "tpa_contact_phone_id"]

    for db_column_name in EmployerExemptionApplication.columns().keys():
        db_column_in_expected_list = db_column_name in expected_col
        assert (
            db_column_in_expected_list
            == EmployerExemptionApplication.is_phone_db_model_foreign_key(db_column_name)
        ), f"{db_column_name} expected {db_column_in_expected_list}, received {not db_column_in_expected_list}"


def test_is_phone_object_name():
    expected_col = ["contact_phone", "tpa_contact_phone"]
    model_columns_and_relationships = (
        list(EmployerExemptionApplication.columns().keys())
        + inspect(EmployerExemptionApplication).relationships.keys()
    )

    for c in model_columns_and_relationships:
        in_expected_list = c in expected_col
        assert in_expected_list == EmployerExemptionApplication.is_phone_object_name(
            c
        ), f"{c} expected {in_expected_list}, received {not in_expected_list}"


def test_get_related_phone_object_name():
    expected_col = {
        "contact_phone_id": "contact_phone",
        "tpa_contact_phone_id": "tpa_contact_phone",
    }

    for db_column_name in EmployerExemptionApplication.columns().keys():
        expected_value = expected_col.get(db_column_name)
        actual_value = EmployerExemptionApplication.get_related_phone_object_name(db_column_name)

        if expected_value is None:
            assert actual_value is None
        else:
            assert actual_value == expected_value


def test_get_related_phone_object(test_db_session, initialize_factories_session):
    employer_exemption_application = EmployerExemptionApplicationFactory.create()

    contact_phone = EmployerExemptionApplication.get_related_phone_object(
        employer_exemption_application, "contact_phone_id"
    )
    tpa_contact_phone = EmployerExemptionApplication.get_related_phone_object(
        employer_exemption_application, "tpa_contact_phone_id"
    )

    assert contact_phone is not None
    assert tpa_contact_phone is not None
    assert isinstance(contact_phone, Phone)
    assert isinstance(tpa_contact_phone, Phone)
    assert contact_phone.phone_id == employer_exemption_application.contact_phone_id
    assert tpa_contact_phone.phone_id == employer_exemption_application.tpa_contact_phone_id

    employer_exemption_db_contact_phone = (
        test_db_session.query(Phone)
        .filter(Phone.phone_id == employer_exemption_application.contact_phone_id)
        .one()
    )
    assert contact_phone.phone_number == employer_exemption_db_contact_phone.phone_number

    employer_exemption_db_tpa_phone = (
        test_db_session.query(Phone)
        .filter(Phone.phone_id == employer_exemption_application.contact_phone_id)
        .one()
    )
    assert tpa_contact_phone.phone_number == employer_exemption_db_tpa_phone.phone_number


def test_get_log_attributes():
    exepcted_col = [
        "are_employer_withholdings_within_allowable_amount",
        "average_workforce_count",
        "created_at",
        "created_by_user_id",
        "does_employer_withhold_premiums",
        "does_plan_cover_all_employees",
        "does_plan_cover_employee_contribution",
        "does_plan_cover_former_employees",
        "does_plan_favor_paid_leave_benefits",
        "does_plan_pay_enough_benefits",
        "does_plan_provide_enough_armed_forces_illness_leave",
        "does_plan_provide_enough_armed_forces_leave",
        "does_plan_provide_enough_bonding_leave",
        "does_plan_provide_enough_caring_leave",
        "does_plan_provide_enough_leave",
        "does_plan_provide_enough_medical_leave",
        "does_plan_provide_intermittent_armed_forces_leave",
        "does_plan_provide_intermittent_bonding_leave",
        "does_plan_provide_intermittent_caring_leave",
        "does_plan_provide_intermittent_medical_leave",
        "does_plan_provide_pfml_job_protection",
        "does_plan_provide_return_to_work_benefits",
        "employer_exemption_application_id",
        "employer_exemption_application_status",
        "employer_id",
        "has_family_exemption",
        "has_medical_exemption",
        "has_obtained_surety_bond",
        "has_third_party_administrator",
        "insurance_plan_effective_at",
        "insurance_plan_expires_at",
        "insurance_plan_name",
        "insurance_provider_name",
        "is_application_status_auto_decided",
        "is_legally_acknowledged",
        "is_self_insured_plan",
        "should_workforce_count_include_1099_misc",
        "submitted_at",
        "surety_bond_amount",
        "surety_company",
        "updated_at",
    ]

    log_attr = EmployerExemptionApplication.get_log_attributes()
    col = log_attr.keys()

    assert len(col) == len(exepcted_col)
    assert sorted(col) == sorted(exepcted_col)
