#
# Tests for massgov.pfml.dor.importer.pending_filing_response.
#

import pathlib
from datetime import date, datetime
from decimal import Decimal
from typing import List

import boto3
import pytest
from sqlalchemy import not_

import massgov.pfml.dor.mock.generate as generator
from massgov.pfml.db.lookup_data.employees import WagesAndContributionsDatasource
from massgov.pfml.db.models.employees import (
    Employee,
    EmployeePushToFineosQueue,
    Employer,
    EmployerPushToFineosQueue,
    EmployerQuarterlyContribution,
    WagesAndContributions,
    WagesAndContributionsHistory,
    WagesAndContributionsUnused,
)
from massgov.pfml.db.models.factories import EmployeeFactory, EmployerFactory, TaxIdentifierFactory
from massgov.pfml.dor.importer.dor_shared_utils import (
    DFML_PROCESSED_FOLDER,
    PROCESSED_FOLDER,
    RECEIVED_FOLDER,
)
from massgov.pfml.dor.importer.employers.employer_importer import import_pending_filing_employers
from massgov.pfml.dor.importer.import_wages import WageImporter
from massgov.pfml.dor.importer.lib import dor_persistence_util
from massgov.pfml.dor.importer.paths import (
    get_exemption_file_to_process,
    get_pending_filing_files_to_process,
)
from massgov.pfml.dor.pending_filing import import_pending_filing as import_pending_filing_dor
from massgov.pfml.dor.pending_filing.pending_filing_response import DFML_RECEIVED_FOLDER
from massgov.pfml.dor.util.process_dor_import import DORImport, ProcessDORImport
from massgov.pfml.util.csv import CSVSourceWrapper
from massgov.pfml.util.encryption import GpgCrypt, PassthruCrypt
from tests.dor.importer import dor_test_data as test_data
from tests.dor.importer.test_import_dor import get_new_import_report

decrypter = PassthruCrypt()
TEST_FOLDER = pathlib.Path(__file__).parent.parent

EMPTY_SSN_TO_EMPLOYEE_ID_MAP = {}


@pytest.mark.timeout(25)
def test_decryption(monkeypatch, test_db_session):
    monkeypatch.setenv("DECRYPT", "true")

    decryption_key = open(TEST_FOLDER / "importer" / "encryption" / "test_private.key").read()
    passphrase = "bb8d58fa-d781-11ea-87d0-0242ac130003"

    decrypter = GpgCrypt([[decryption_key, passphrase]])

    employer_file_path = (
        TEST_FOLDER / "importer" / "encryption" / "DORDUADFML_SUBMISSION_20211210131901"
    )
    exemption_file_path = TEST_FOLDER / "importer" / "CompaniesReturningToStatePlan.csv"

    import_files = list()
    import_files.append(str(employer_file_path))

    reports = import_pending_filing_dor.process_pending_filing_employer_files(
        import_files=import_files,
        exemption_file_path=str(exemption_file_path),
        decrypt_files=True,
        optional_decrypter=decrypter,
        db_session=test_db_session,
    )

    employer_count = 4
    employee_count = 4

    assert reports[0].created_employers_count == employer_count
    assert reports[0].created_employees_count == employee_count
    assert reports[0].created_wages_and_contributions_count == employee_count


def test_account_key_set_single_file(monkeypatch, test_db_session):
    employer_file_path = (
        TEST_FOLDER / "importer" / "encryption" / "DORDUADFML_SUBMISSION_20211210131901"
    )
    exemption_file_path = TEST_FOLDER / "importer" / "CompaniesReturningToStatePlan.csv"

    monkeypatch.setenv("DECRYPT", "true")

    decryption_key = open(TEST_FOLDER / "importer" / "encryption" / "test_private.key").read()
    passphrase = "bb8d58fa-d781-11ea-87d0-0242ac130003"

    decrypter = GpgCrypt([[decryption_key, passphrase]])

    dor_import = DORImport(
        decrypter=decrypter,
        employer_file_path=str(employer_file_path),
    )
    process_dor_import = ProcessDORImport(dor_import)

    exemption_data = CSVSourceWrapper(str(exemption_file_path))
    employers, employees = process_dor_import.parse_pending_filing_employer_file(
        test_db_session, exemption_data
    )

    assert employees[0]["account_key"] == employers[0]["account_key"]
    assert employers[0]["account_key"] != employers[3]["account_key"]
    assert employees[3]["account_key"] == employers[3]["account_key"]


def test_employer_multiple_wage_rows(initialize_factories_session, monkeypatch, test_db_session):
    monkeypatch.setenv("DECRYPT", "false")

    employer_file_path = TEST_FOLDER / "importer" / "DORDUADFML_SUBMISSION_20212216"
    exemption_file_path = TEST_FOLDER / "importer" / "CompaniesReturningToStatePlan.csv"

    import_files = list()
    import_files.append(str(employer_file_path))

    import_pending_filing_dor.process_pending_filing_employer_files(
        import_files=import_files,
        exemption_file_path=str(exemption_file_path),
        decrypt_files=True,
        optional_decrypter=decrypter,
        db_session=test_db_session,
    )

    employer = test_db_session.query(Employer).filter(Employer.employer_fein == "*********").first()

    employee = (
        test_db_session.query(Employee)
        .filter(Employee.first_name == "TEST" and Employee.last_name == "DOE")
        .first()
    )

    wages = (
        test_db_session.query(WagesAndContributions)
        .filter(
            WagesAndContributions.employee_id == employee.employee_id
            and WagesAndContributions.employer_id == employer.employer_id
        )
        .all()
    )

    assert len(wages) == 4

    employer = test_db_session.query(Employer).filter(Employer.employer_fein == "123456789").first()
    employer2 = (
        test_db_session.query(Employer).filter(Employer.employer_fein == "123456799").first()
    )
    employer3 = (
        test_db_session.query(Employer).filter(Employer.employer_fein == "*********").first()
    )

    assert employer2.family_exemption is True
    assert employer2.medical_exemption is True

    assert employer3.family_exemption is True
    assert employer3.medical_exemption is True

    wage_rows = (
        test_db_session.query(EmployerQuarterlyContribution)
        .filter(EmployerQuarterlyContribution.employer_id == employer.employer_id)
        .all()
    )
    wage_rows2 = (
        test_db_session.query(EmployerQuarterlyContribution)
        .filter(EmployerQuarterlyContribution.employer_id == employer2.employer_id)
        .all()
    )
    wage_rows3 = (
        test_db_session.query(EmployerQuarterlyContribution)
        .filter(EmployerQuarterlyContribution.employer_id == employer3.employer_id)
        .all()
    )

    assert len(wage_rows) == 3
    assert len(wage_rows2) == 2
    assert len(wage_rows3) == 4

    queue_item = (
        test_db_session.query(EmployerPushToFineosQueue)
        .filter(
            EmployerPushToFineosQueue.employer_id == employer2.employer_id
            and EmployerPushToFineosQueue.action == "INSERT"
        )
        .first()
    )

    cease_date = datetime.strptime("1/30/2022", "%m/%d/%Y").date()
    assert queue_item.exemption_cease_date == cease_date


def test_update_existing_employer_cease_date(
    initialize_factories_session, monkeypatch, test_db_session
):
    monkeypatch.setenv("DECRYPT", "true")

    decryption_key = open(TEST_FOLDER / "importer" / "encryption" / "test_private.key").read()
    passphrase = "bb8d58fa-d781-11ea-87d0-0242ac130003"

    decrypter = GpgCrypt([[decryption_key, passphrase]])

    cease_date = datetime.strptime("1/1/2025", "%m/%d/%Y").date()
    employer = EmployerFactory.create(employer_fein="100000001", exemption_cease_date=cease_date)

    employer_file_path = (
        TEST_FOLDER / "importer" / "encryption" / "DORDUADFML_SUBMISSION_20211210131901"
    )
    exemption_file_path = TEST_FOLDER / "importer" / "CompaniesReturningToStatePlan.csv"

    import_files = list()
    import_files.append(str(employer_file_path))

    import_pending_filing_dor.process_pending_filing_employer_files(
        import_files=import_files,
        exemption_file_path=str(exemption_file_path),
        decrypt_files=True,
        optional_decrypter=decrypter,
        db_session=test_db_session,
    )

    queue_item = (
        test_db_session.query(EmployerPushToFineosQueue)
        .filter(EmployerPushToFineosQueue.employer_id == employer.employer_id)
        .first()
    )

    cease_date = datetime.strptime("1/1/2022", "%m/%d/%Y").date()
    assert queue_item.exemption_cease_date == cease_date


def test_get_csv_regex(monkeypatch, mock_dfml_s3_bucket):
    exemption_file_name = "CompaniesReturningToStatePlan1.csv"

    exemption_file_path = TEST_FOLDER / "importer" / "CompaniesReturningToStatePlan.csv"
    exemption_file = open(exemption_file_path, "rb")
    exemption_key = "{}{}".format(DFML_RECEIVED_FOLDER, exemption_file_name)

    s3 = boto3.client("s3")
    s3.put_object(Bucket=mock_dfml_s3_bucket, Key=exemption_key, Body=exemption_file.read())
    full_dfml_received_folder_path = "s3://{}/{}".format(mock_dfml_s3_bucket, DFML_RECEIVED_FOLDER)

    exception_file_found = get_exemption_file_to_process(full_dfml_received_folder_path)
    assert (
        exception_file_found
        == "s3://test_dfml_bucket/dfml/received/CompaniesReturningToStatePlan1.csv"
    )


@pytest.fixture
def mock_s3_bucket_resource(mock_s3):
    bucket = mock_s3.Bucket("test_dfml_bucket")
    bucket.create()
    yield bucket


@pytest.fixture
def mock_dfml_s3_bucket(mock_s3_bucket_resource):
    yield mock_s3_bucket_resource.name


def test_insert_wage_rows_existing_employee(
    initialize_factories_session, monkeypatch, test_db_session
):
    monkeypatch.setenv("DECRYPT", "false")

    employer_file_path = TEST_FOLDER / "importer" / "DORDUADFML_SUBMISSION_20212216"
    exemption_file_path = TEST_FOLDER / "importer" / "CompaniesReturningToStatePlan.csv"

    account_key = "*********"
    employer = EmployerFactory.create(account_key=account_key, employer_fein="*********")
    tax_identifier = TaxIdentifierFactory.create(tax_identifier="*********")
    employee = EmployeeFactory.create(tax_identifier=tax_identifier, email_address="<EMAIL>")
    filing_period = datetime.strptime("9/30/2020", "%m/%d/%Y").date()
    wage_row = WagesAndContributions()
    wage_row.account_key = account_key
    wage_row.is_independent_contractor = False
    wage_row.is_opted_in = False
    wage_row.employer_med_contribution = Decimal(12345)
    wage_row.employer_fam_contribution = Decimal(67890)
    wage_row.employee_med_contribution = Decimal(12345)
    wage_row.employee_fam_contribution = Decimal(67890)
    wage_row.employee_ytd_wages = Decimal(100)
    wage_row.employee_qtr_wages = Decimal(100)
    wage_row.filing_period = filing_period
    wage_row.employer_id = employer.employer_id
    wage_row.employee_id = employee.employee_id
    wage_row.wages_and_contributions_datasource_id = (
        WagesAndContributionsDatasource.DFML_REPORTED_WAGES.wages_and_contributions_datasource_id
    )
    test_db_session.add(wage_row)

    test_db_session.commit()

    wages = (
        test_db_session.query(WagesAndContributions)
        .filter(
            WagesAndContributions.employee_id == employee.employee_id
            and WagesAndContributions.employer_id == employer.employer_id
        )
        .all()
    )

    assert len(wages) == 1

    import_files = list()
    import_files.append(str(employer_file_path))

    import_pending_filing_dor.process_pending_filing_employer_files(
        import_files=import_files,
        exemption_file_path=str(exemption_file_path),
        decrypt_files=True,
        optional_decrypter=decrypter,
        db_session=test_db_session,
    )

    employer = test_db_session.query(Employer).filter(Employer.employer_fein == "*********").first()

    assert employer.account_key == account_key

    employee = (
        test_db_session.query(Employee)
        .filter(Employee.tax_identifier_id == tax_identifier.tax_identifier_id)
        .first()
    )

    wages = (
        test_db_session.query(WagesAndContributions)
        .filter(
            WagesAndContributions.employee_id == employee.employee_id
            and WagesAndContributions.employer_id == employer.employer_id
        )
        .all()
    )

    # this means 3 new wages rows were created and the previously created one was not updated
    assert len(wages) == 4

    assert wages[0].filing_period == filing_period
    assert wages[0].employer_med_contribution == Decimal(12345)
    assert wages[0].employer_fam_contribution == Decimal(67890)

    unmodified_employee = (
        test_db_session.query(Employee)
        .filter(Employee.employee_id == employee.employee_id)
        .one_or_none()
    )

    assert unmodified_employee.email_address == "<EMAIL>"


@pytest.mark.timeout(60)
def test_e2e(monkeypatch, test_db_session, mock_s3_bucket, mock_dfml_s3_bucket):
    file_name = "DORDUADFML_SUBMISSION_20211210131901"
    exemption_file_name = "CompaniesReturningToStatePlan.csv"

    employer_file_path = (
        TEST_FOLDER / "importer" / "encryption" / "DORDUADFML_SUBMISSION_20211210131901"
    )
    exemption_file_path = TEST_FOLDER / "importer" / "CompaniesReturningToStatePlan.csv"

    employer_file = open(employer_file_path, "rb")
    exemption_file = open(exemption_file_path, "rb")

    monkeypatch.setenv("DECRYPT", "true")

    decryption_key = open(TEST_FOLDER / "importer" / "encryption" / "test_private.key").read()
    passphrase = "bb8d58fa-d781-11ea-87d0-0242ac130003"

    decrypter = GpgCrypt([[decryption_key, passphrase]])

    key = "{}{}".format(RECEIVED_FOLDER, file_name)
    exemption_key = "{}{}".format(DFML_RECEIVED_FOLDER, exemption_file_name)

    moved_key = "{}{}".format(PROCESSED_FOLDER, file_name)
    moved_exemption_key = "{}{}".format(DFML_PROCESSED_FOLDER, exemption_file_name)
    full_received_folder_path = "s3://{}/{}".format(mock_s3_bucket, RECEIVED_FOLDER)
    full_dfml_received_folder_path = "s3://{}/{}".format(mock_dfml_s3_bucket, DFML_RECEIVED_FOLDER)

    s3 = boto3.client("s3")
    s3.put_object(Bucket=mock_s3_bucket, Key=key, Body=employer_file.read())
    s3.put_object(Bucket=mock_dfml_s3_bucket, Key=exemption_key, Body=exemption_file.read())

    should_exist_1 = s3.head_object(Bucket=mock_s3_bucket, Key=key)
    assert should_exist_1 is not None

    should_exist_2 = s3.head_object(Bucket=mock_dfml_s3_bucket, Key=exemption_key)
    assert should_exist_2 is not None

    import_files = get_pending_filing_files_to_process(full_received_folder_path)
    assert (
        import_files[0] == "s3://test_dfml_bucket/dor/received/DORDUADFML_SUBMISSION_20211210131901"
    )

    path_without_slash = full_received_folder_path[:-1]
    assert path_without_slash == "s3://test_dfml_bucket/dor/received"
    import_files_without_slash = get_pending_filing_files_to_process(path_without_slash)
    assert (
        import_files_without_slash[0]
        == "s3://test_dfml_bucket/dor/received/DORDUADFML_SUBMISSION_20211210131901"
    )

    assert len(import_files) == 1

    exception_file = get_exemption_file_to_process(full_dfml_received_folder_path)

    reports = import_pending_filing_dor.process_pending_filing_employer_files(
        import_files=import_files,
        exemption_file_path=exception_file,
        decrypt_files=True,
        optional_decrypter=decrypter,
        db_session=test_db_session,
        optional_s3=s3,
    )

    employer_count = 4
    employee_count = 4

    assert reports[0].created_employers_count == employer_count
    assert reports[0].created_employees_count == employee_count
    assert reports[0].created_wages_and_contributions_count == employee_count

    should_exist_3 = s3.head_object(Bucket=mock_s3_bucket, Key=moved_key)
    assert should_exist_3 is not None

    should_exist_4 = s3.head_object(Bucket=mock_dfml_s3_bucket, Key=moved_exemption_key)
    assert should_exist_4 is not None


def test_record_employees_with_new_employers(test_db_session, monkeypatch):

    # Employee generate helper
    def generate_employee_and_wage_item(id, employer):
        employee = next(generator.generate_single_employee(id, [employer]))
        # convert quarter to date
        employee["filing_period"] = employee["filing_period"].as_date()
        return employee

    # Create two employers
    employer1 = generator.generate_single_employer(1)
    employer2 = generator.generate_single_employer(2)
    employers = [employer1, employer2]
    for employer in employers:
        employer["family_exemption"] = employer["medical_exemption"] = True
        employer["exemption_commence_date"] = date(2024, 1, 1)
        employer["exemption_cease_date"] = date(2024, 12, 31)

    report, report_log_entry = get_new_import_report(test_db_session)
    exemption_path = TEST_FOLDER / "importer" / "CompaniesReturningToStatePlan.csv"
    exemption_data = CSVSourceWrapper(exemption_path)
    import_pending_filing_employers(
        test_db_session, employers, exemption_data, report, report_log_entry.import_log_id
    )

    created_employers = test_db_session.query(Employer).all()
    assert len(created_employers) == 2

    # Verify Employer Logs are correct (1)
    employer_insert_logs1: List[EmployerPushToFineosQueue] = (
        test_db_session.query(EmployerPushToFineosQueue)
        .filter(EmployerPushToFineosQueue.action == "INSERT")
        .all()
    )
    assert len(employer_insert_logs1) == 2

    found_employer_1 = next(
        x for x in employer_insert_logs1 if x.employer_id == created_employers[0].employer_id
    )
    assert found_employer_1

    found_employer_2 = next(
        x for x in employer_insert_logs1 if x.employer_id == created_employers[1].employer_id
    )
    assert found_employer_2

    # ------
    employer_update_logs1: List[EmployerPushToFineosQueue] = (
        test_db_session.query(EmployerPushToFineosQueue)
        .filter(EmployerPushToFineosQueue.action == "UPDATE")
        .all()
    )
    assert len(employer_update_logs1) == 0
    # ------------

    # Create two employees
    employee1 = generate_employee_and_wage_item(1, employer1)
    employee2 = generate_employee_and_wage_item(2, employer2)

    employee_ssns_to_id_created_in_current_import_run = {}

    report1, report_log_entry1 = get_new_import_report(test_db_session)
    wage_import1 = WageImporter(
        test_db_session,
        [employee1, employee2],
        employee_ssns_to_id_created_in_current_import_run,
        report1,
        list(),
        report_log_entry1.import_log_id,
        WagesAndContributionsDatasource.PENDING_FILING,
    )
    wage_import1.import_employees_and_wage_data(should_update_wages=False)

    assert report1.created_employees_count == 2
    assert report1.logged_employees_for_new_employer == 0

    created_employees = test_db_session.query(Employee).all()
    assert len(created_employees) == 2

    created_wages = test_db_session.query(WagesAndContributions).all()
    assert len(created_wages) == 2

    created_wage_histories = test_db_session.query(WagesAndContributionsHistory).all()
    assert len(created_wage_histories) == 2

    # Verify Employee Logs are correct (1)
    employee_insert_logs1: List[EmployeePushToFineosQueue] = (
        test_db_session.query(EmployeePushToFineosQueue)
        .filter(EmployeePushToFineosQueue.action == "INSERT")
        .all()
    )
    employee_insert_log_ids1 = [x.employee_push_to_fineos_queue_id for x in employee_insert_logs1]

    assert len(employee_insert_logs1) == 2

    found_employee_1 = next(
        x for x in employee_insert_logs1 if x.employee_id == created_employees[0].employee_id
    )
    assert found_employee_1

    found_employee_2 = next(
        x for x in employee_insert_logs1 if x.employee_id == created_employees[1].employee_id
    )
    assert found_employee_2
    # ------
    employee_update_logs1: List[EmployeePushToFineosQueue] = (
        test_db_session.query(EmployeePushToFineosQueue)
        .filter(EmployeePushToFineosQueue.action == "UPDATE")
        .all()
    )
    assert len(employee_update_logs1) == 0
    # ------
    employee_employer_logs1: List[EmployeePushToFineosQueue] = (
        test_db_session.query(EmployeePushToFineosQueue)
        .filter(EmployeePushToFineosQueue.action == "UPDATE_NEW_EMPLOYER")
        .all()
    )
    assert len(employee_employer_logs1) == 0
    # ------------

    # Simulate a wage entry for an existing employee with a new employer
    employee2_employer1 = generate_employee_and_wage_item(2, employer1)
    employee2_employer1_second_entry = generate_employee_and_wage_item(2, employer1)
    employee3 = generate_employee_and_wage_item(3, employer1)
    employee_ssns_to_id_created_in_current_import_run = {}

    report2, report_log_entry2 = get_new_import_report(test_db_session)
    wage_import2 = WageImporter(
        test_db_session,
        [employee1, employee2, employee2_employer1, employee2_employer1_second_entry, employee3],
        employee_ssns_to_id_created_in_current_import_run,
        report2,
        list(),
        report_log_entry2.import_log_id,
        WagesAndContributionsDatasource.PENDING_FILING,
    )
    wage_import2.import_employees_and_wage_data(should_update_wages=False)

    assert report2.unmodified_employees_count == 2
    assert report2.created_employees_count == 1
    assert report2.logged_employees_for_new_employer == 1

    created_wage_histories = test_db_session.query(WagesAndContributionsHistory).all()
    assert len(created_wage_histories) == 5

    employee_with_new_employer = dor_persistence_util.get_employees_by_ssn(
        test_db_session, [employee2_employer1["employee_ssn"]]
    )[0]

    created_employee = dor_persistence_util.get_employees_by_ssn(
        test_db_session, [employee3["employee_ssn"]]
    )[0]

    # Verify Employee Logs are correct (2)
    employee_insert_logs2: List[EmployeePushToFineosQueue] = (
        test_db_session.query(EmployeePushToFineosQueue)
        .filter(
            EmployeePushToFineosQueue.action == "INSERT",
            not_(
                EmployeePushToFineosQueue.employee_push_to_fineos_queue_id.in_(
                    employee_insert_log_ids1
                )
            ),
        )
        .all()
    )
    assert len(employee_insert_logs2) == 1
    assert employee_insert_logs2[0].employee_id == created_employee.employee_id
    # ------
    employee_update_logs2: List[EmployeePushToFineosQueue] = (
        test_db_session.query(EmployeePushToFineosQueue)
        .filter(EmployeePushToFineosQueue.action == "UPDATE")
        .all()
    )
    assert len(employee_update_logs2) == 0
    # ------
    employee_employer_logs2: List[EmployeePushToFineosQueue] = (
        test_db_session.query(EmployeePushToFineosQueue)
        .filter(EmployeePushToFineosQueue.action == "UPDATE_NEW_EMPLOYER")
        .all()
    )
    assert len(employee_employer_logs2) == 1
    assert employee_employer_logs2[0].employer_id == found_employer_1.employer_id
    assert employee_employer_logs2[0].employee_id == employee_with_new_employer.employee_id
    # ------------


def test_import_pending_filing_does_not_overwrite_dfml_wages(
    initialize_factories_session, test_db_session
):
    filing_period = date(2024, 12, 31)

    existing_boolean = False
    existing_datasource = WagesAndContributionsDatasource.DFML_REPORTED_WAGES
    existing_wage_amount = Decimal("1.00")
    existing_employee, existing_employer = (
        test_data.generate_existing_entities_for_wage_and_contributions_testing(
            existing_boolean,
            existing_datasource,
            filing_period,
            existing_wage_amount,
            existing_wage_amount,
        )[:2]
    )

    new_boolean = True
    new_wage_amount = Decimal("2.00")
    new_wage_data = test_data.generate_new_wage_data_for_wages_and_contributions_testing(
        new_boolean, existing_employee, existing_employer, filing_period, new_wage_amount
    )
    new_datasource = WagesAndContributionsDatasource.PENDING_FILING

    import_report, import_log = get_new_import_report(test_db_session)

    wage_import = WageImporter(
        test_db_session,
        [new_wage_data],
        {},
        import_report,
        [],
        import_log.import_log_id,
        new_datasource,
    )

    wage_import.import_employees_and_wage_data(record_new_employees=True)

    wages_and_contributions_records = test_db_session.query(WagesAndContributions).all()
    assert len(wages_and_contributions_records) == 1
    wages_and_contributions = wages_and_contributions_records[0]
    assert (
        wages_and_contributions.wages_and_contributions_datasource_id
        == existing_datasource.wages_and_contributions_datasource_id
    )
    assert wages_and_contributions.employee_fam_contribution == existing_wage_amount
    assert wages_and_contributions.employee_med_contribution == existing_wage_amount
    assert wages_and_contributions.employee_qtr_wages == existing_wage_amount
    assert wages_and_contributions.employee_ytd_wages == existing_wage_amount
    assert wages_and_contributions.employer_fam_contribution == existing_wage_amount
    assert wages_and_contributions.employer_med_contribution == existing_wage_amount
    assert wages_and_contributions.filing_period == filing_period
    assert wages_and_contributions.is_independent_contractor == existing_boolean
    assert wages_and_contributions.is_opted_in == existing_boolean

    wages_and_contributions_unused_records = test_db_session.query(
        WagesAndContributionsUnused
    ).all()
    assert len(wages_and_contributions_unused_records) == 0
