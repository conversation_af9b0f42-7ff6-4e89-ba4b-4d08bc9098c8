#
# Tests for massgov.pfml.dor.importer.dor_shared_utils.
#

import pathlib
from datetime import datetime
from typing import List

import pytest

from massgov.pfml.db.models.employees import (
    Address,
    Employee,
    Employer,
    EmployerAddress,
    EmployerPushToFineosQueue,
    EmployerQuarterlyContribution,
    WagesAndContributions,
    WagesAndContributionsHistory,
)
from massgov.pfml.db.models.employer_exemptions import InsuranceProvider
from massgov.pfml.dor.importer.dor_handler_utils import run_import
from massgov.pfml.dor.importer.dor_shared_utils import ImportFiles
from massgov.pfml.dor.importer.import_exempt_dor import import_full

from . import dor_test_data as test_data

employee_file = "DORDFML_20200519120622"
employer_file = "DORDFMLEMP_20200519120622"
exempt_employer_file = "DORDUADFML_20200519120622"
exempt_employer_file2 = "DORDUADFML_20220519120622"
TEST_FOLDER = pathlib.Path(__file__).parent.parent


# Use a persistent database for all the tests here, as they need to support multiple concurrent
# connections. This means we can't use a transaction based fixture, so wipe relevant tables before
# each individual test function.
@pytest.fixture(scope="function", autouse=True)
def clear_tables(module_persistent_db, module_persistent_db_session):
    module_persistent_db_session.query(EmployerAddress).delete()
    module_persistent_db_session.query(EmployerQuarterlyContribution).delete()
    module_persistent_db_session.query(WagesAndContributionsHistory).delete()
    module_persistent_db_session.query(WagesAndContributions).delete()
    module_persistent_db_session.query(Employer).delete()
    module_persistent_db_session.query(EmployerPushToFineosQueue).delete()
    module_persistent_db_session.commit()


@pytest.fixture
def mock_initial_path(monkeypatch, tmp_path):
    employer_quarter_line = test_data.get_employer_quarter_line()
    employee_quarter_line = test_data.get_employee_quarter_line()
    content1 = "{}\n{}".format(employer_quarter_line, employee_quarter_line)

    content2 = test_data.get_employer_info_line()

    test_folder = tmp_path / "test_folder"
    test_folder.mkdir()
    test_file = test_folder / employee_file
    test_file.write_text(content1)
    test_file2 = test_folder / employer_file
    test_file2.write_text(content2)

    monkeypatch.setenv("FOLDER_PATH", str(test_folder))


@pytest.fixture
def mock_exempt_path(monkeypatch, tmp_path):
    employer_quarter_line = test_data.get_exempt_employer_info_line()
    employee_quarter_line = test_data.get_exempt_employee_wage_data_line()
    content1 = "{}\n{}".format(employer_quarter_line, employee_quarter_line)

    content2 = test_data.get_exempt_employer_info_line()

    test_folder = tmp_path / "test_folder"
    test_folder.mkdir()
    test_file = test_folder / exempt_employer_file
    test_file.write_text(content1)
    test_file2 = test_folder / exempt_employer_file2
    test_file2.write_text(content2)

    monkeypatch.setenv("FOLDER_PATH", str(test_folder))


@pytest.fixture
def mock_pending_filing_path(monkeypatch):
    employer_file_folder = f"{TEST_FOLDER}/importer/"
    exemption_file_folder = f"{TEST_FOLDER}/importer/"

    monkeypatch.setenv("FOLDER_PATH", str(employer_file_folder))
    monkeypatch.setenv("CSV_FOLDER_PATH", str(exemption_file_folder))


def test_initial_handler(mock_initial_path, module_persistent_db_session):
    run_import(ImportFiles(file_type="initial"), module_persistent_db_session)
    employer_in_db: Employer = (
        module_persistent_db_session.query(Employer)
        .filter(Employer.employer_fein == "*********")
        .first()
    )
    assert employer_in_db.employer_dba == "Anderson, Barber and Johnson"
    assert employer_in_db.account_key == "***********"
    employer_address: Address = (
        module_persistent_db_session.query(Address)
        .join(EmployerAddress)
        .filter(Employer.employer_fein == "*********")
        .one()
    )
    assert employer_address.city == "North Kaylabury"
    address_count = (
        module_persistent_db_session.query(Address)
        .outerjoin(InsuranceProvider)
        .filter(InsuranceProvider.address_id == None)  # noqa: E711
        .count()
    )
    assert address_count == 1


def test_pending_filling_handler(mock_pending_filing_path, module_persistent_db_session):
    run_import(ImportFiles(file_type="pending_filing"), module_persistent_db_session)

    employer: Employer = (
        module_persistent_db_session.query(Employer)
        .filter(Employer.employer_fein == "*********")
        .first()
    )

    employee: Employee = (
        module_persistent_db_session.query(Employee)
        .filter(Employee.first_name == "TEST" and Employee.last_name == "DOE")
        .first()
    )

    wages: List[WagesAndContributions] = (
        module_persistent_db_session.query(WagesAndContributions)
        .filter(
            WagesAndContributions.employee_id == employee.employee_id
            and WagesAndContributions.employer_id == employer.employer_id
        )
        .all()
    )

    assert len(wages) == 4

    employer1: Employer = (
        module_persistent_db_session.query(Employer)
        .filter(Employer.employer_fein == "123456789")
        .first()
    )
    employer2: Employer = (
        module_persistent_db_session.query(Employer)
        .filter(Employer.employer_fein == "123456799")
        .first()
    )
    employer3: Employer = (
        module_persistent_db_session.query(Employer)
        .filter(Employer.employer_fein == "*********")
        .first()
    )

    assert employer2.family_exemption is True
    assert employer2.medical_exemption is True

    assert employer3.family_exemption is True
    assert employer3.medical_exemption is True

    wage_rows1: List[EmployerQuarterlyContribution] = (
        module_persistent_db_session.query(EmployerQuarterlyContribution)
        .filter(EmployerQuarterlyContribution.employer_id == employer1.employer_id)
        .all()
    )
    wage_rows2: List[EmployerQuarterlyContribution] = (
        module_persistent_db_session.query(EmployerQuarterlyContribution)
        .filter(EmployerQuarterlyContribution.employer_id == employer2.employer_id)
        .all()
    )
    wage_rows3: List[EmployerQuarterlyContribution] = (
        module_persistent_db_session.query(EmployerQuarterlyContribution)
        .filter(EmployerQuarterlyContribution.employer_id == employer3.employer_id)
        .all()
    )

    assert len(wage_rows1) == 3
    assert len(wage_rows2) == 2
    assert len(wage_rows3) == 4

    queue_item: EmployerPushToFineosQueue = (
        module_persistent_db_session.query(EmployerPushToFineosQueue)
        .filter(
            EmployerPushToFineosQueue.employer_id == employer2.employer_id
            and EmployerPushToFineosQueue.action == "INSERT"
        )
        .first()
    )
    cease_date = datetime.strptime("1/30/2022", "%m/%d/%Y").date()
    assert queue_item.exemption_cease_date == cease_date


def test_exempt_handler(mock_exempt_path, module_persistent_db_session):
    run_import(ImportFiles(file_type="exempt", importer=import_full), module_persistent_db_session)

    employer_in_db: Employer = (
        module_persistent_db_session.query(Employer)
        .filter(Employer.employer_fein == "*********")
        .one_or_none()
    )
    assert employer_in_db is not None
    assert employer_in_db.employer_name == "Anderson, Barber and Johnson"
    assert employer_in_db.account_key.startswith("exempt_employer_")
