#
# Tests for massgov.pfml.dor.importer.import_exempt_dor.
#

import datetime
import pathlib
from decimal import Decimal
from typing import List
from unittest import mock

import pytest
from sqlalchemy import not_

import massgov.pfml.dor.importer.import_exempt_dor as import_exempt_dor
import massgov.pfml.dor.importer.lib.dor_persistence_util as dor_persistence_util
import massgov.pfml.dor.mock.generate as generator
from massgov.pfml.db import Session
from massgov.pfml.db.lookup_data.employees import WagesAndContributionsDatasource
from massgov.pfml.db.models.employees import (
    Employee,
    EmployeePushToFineosQueue,
    Employer,
    EmployerPushToFineosQueue,
    WagesAndContributions,
    WagesAndContributionsHistory,
    WagesAndContributionsUnused,
)
from massgov.pfml.dor.importer.dor_file_formats import (
    EMPLOYEE_FORMAT,
    EMPLOYER_PENDING_FILING_RESPONSE_FILE_FORMAT_B,
)
from massgov.pfml.dor.importer.employers.employer_importer import (
    import_employers,
    import_exempt_employers,
)
from massgov.pfml.dor.importer.import_wages import WageImporter
from massgov.pfml.dor.util.process_dor_import import DORImport, ProcessDORImport
from massgov.pfml.util.encryption import PassthruCrypt
from tests.dor.importer.dor_test_data import b_records, pending_filing_b_records
from tests.dor.importer.test_import_dor import (
    get_new_import_report,
    validate_employee_persistence,
    validate_employer_persistence,
    validate_wage_persistence,
)

from . import dor_test_data as test_data

decrypter = PassthruCrypt()
exempt_employer_file = "DORDUADFML_20200519120622"
exempt_employer_file2 = "DORDUADFML_20220519120622"

TEST_FOLDER = pathlib.Path(__file__).parent

EMPTY_SSN_TO_EMPLOYEE_ID_MAP = {}


@pytest.fixture
def test_fs_path(tmp_path):
    employer_quarter_line = test_data.get_exempt_employer_info_line()
    employee_quarter_line = test_data.get_exempt_employee_wage_data_line()
    content1 = "{}\n{}".format(employer_quarter_line, employee_quarter_line)

    content2 = test_data.get_exempt_employer_info_line()

    test_folder = tmp_path / "test_folder"
    test_folder.mkdir()
    test_file = test_folder / exempt_employer_file
    test_file.write_text(content1)
    test_file2 = test_folder / exempt_employer_file2
    test_file2.write_text(content2)

    return test_folder


# @mock.patch("massgov.pfml.dor.importer.import_exempt_dor.gen_employer_account_key")
@mock.patch("massgov.pfml.dor.util.process_dor_import.gen_employer_account_key")
def test_parse_employer_file(mock_gen_uuid, test_db_session, test_fs_path):
    employer_info = test_data.get_new_exempt_employer()
    mock_gen_uuid.return_value = employer_info["account_key"]
    employer_file_path = "{}/{}".format(str(test_fs_path), exempt_employer_file)

    dor_import = DORImport(
        employer_file_path=employer_file_path,
        employee_file_path=employer_file_path,
        decrypter=decrypter,
    )
    process_dor_import = ProcessDORImport(dor_import)
    employers_info, employees_info = process_dor_import.parse_exempt_employer_file(test_db_session)

    assert employers_info[0] == employer_info


def test_employer_import(test_db_session):
    # perform import
    employer_payload = test_data.get_new_exempt_employer(gen_mtc_number=True)
    employers = [employer_payload]
    report, report_log_entry = get_new_import_report(test_db_session)
    assert report_log_entry.import_log_id is not None

    import_exempt_employers(test_db_session, employers, report, report_log_entry.import_log_id)

    # confirm expected columns are persisted
    persisted_employer = (
        test_db_session.query(Employer)
        .filter(Employer.account_key == employer_payload["account_key"])
        .one_or_none()
    )
    employer_id = persisted_employer.employer_id

    assert persisted_employer is not None

    validate_employer_persistence(
        employer_payload, persisted_employer, report_log_entry.import_log_id
    )

    assert report.created_employers_count == 1
    assert report.updated_employers_count == 0
    assert report.unmodified_employers_count == 0

    # Verify Logs are correct
    employer_insert_logs: List[EmployerPushToFineosQueue] = (
        test_db_session.query(EmployerPushToFineosQueue)
        .filter(EmployerPushToFineosQueue.action == "INSERT")
        .all()
    )
    assert len(employer_insert_logs) == 1
    assert employer_insert_logs[0].employer_id == employer_id
    # ------
    employer_update_logs: List[EmployerPushToFineosQueue] = (
        test_db_session.query(EmployerPushToFineosQueue)
        .filter(EmployerPushToFineosQueue.action == "UPDATE")
        .all()
    )
    assert len(employer_update_logs) == 0


def test_employee_wage_data_create(test_db_session, monkeypatch):
    # create empty report
    report, report_log_entry = get_new_import_report(test_db_session)

    # create employer dependency
    employer_payload = test_data.get_new_exempt_employer(gen_mtc_number=True)
    account_key = employer_payload["account_key"]
    employers = [employer_payload]
    import_exempt_employers(test_db_session, employers, report, report_log_entry.import_log_id)

    persisted_employer = (
        test_db_session.query(Employer).filter(Employer.account_key == account_key).one_or_none()
    )
    employer_id = persisted_employer.employer_id

    # Verify Employer Logs are correct
    employer_insert_logs: List[EmployerPushToFineosQueue] = (
        test_db_session.query(EmployerPushToFineosQueue)
        .filter(EmployerPushToFineosQueue.action == "INSERT")
        .all()
    )
    assert len(employer_insert_logs) == 1
    assert employer_insert_logs[0].employer_id == employer_id
    # ------
    employer_update_logs: List[EmployerPushToFineosQueue] = (
        test_db_session.query(EmployerPushToFineosQueue)
        .filter(EmployerPushToFineosQueue.action == "UPDATE")
        .all()
    )
    assert len(employer_update_logs) == 0
    # ------------

    # perform employee and wage import
    employee_wage_data_payload = test_data.get_new_exempt_employee_wage_data()

    employee_id_by_ssn = {}
    wage_import = WageImporter(
        test_db_session,
        [employee_wage_data_payload],
        employee_id_by_ssn,
        report,
        list(),
        report_log_entry.import_log_id,
        WagesAndContributionsDatasource.EXEMPTION_FILED,
    )
    wage_import.import_employees_and_wage_data(is_exempt_import=True)

    employee_id = employee_id_by_ssn[employee_wage_data_payload["employee_ssn"]]
    persisted_employee = test_db_session.get(Employee, employee_id)

    assert persisted_employee is not None
    validate_employee_persistence(
        employee_wage_data_payload, persisted_employee, report_log_entry.import_log_id
    )

    persisted_wage_info = (
        dor_persistence_util.get_wages_and_contributions_by_employee_id_and_filling_period(
            test_db_session,
            employee_id,
            employer_id,
            employee_wage_data_payload["filing_period"],
        )
    )

    assert persisted_wage_info is not None
    validate_wage_persistence(
        employee_wage_data_payload, persisted_wage_info, report_log_entry.import_log_id
    )

    assert report.created_employees_count == 1
    assert report.updated_employees_count == 0

    assert report.created_wages_and_contributions_count == 1
    assert report.updated_wages_and_contributions_count == 0

    created_wage_histories = test_db_session.query(WagesAndContributionsHistory).all()
    assert len(created_wage_histories) == 1

    # Verify Employee Logs are correct
    employee_insert_logs: List[EmployeePushToFineosQueue] = (
        test_db_session.query(EmployeePushToFineosQueue)
        .filter(EmployeePushToFineosQueue.action == "INSERT")
        .all()
    )
    assert len(employee_insert_logs) == 1
    assert employee_insert_logs[0].employee_id == employee_id
    # ------
    employee_update_logs: List[EmployeePushToFineosQueue] = (
        test_db_session.query(EmployeePushToFineosQueue)
        .filter(EmployeePushToFineosQueue.action == "UPDATE")
        .all()
    )
    assert len(employee_update_logs) == 0
    # ------
    employee_employer_logs: List[EmployeePushToFineosQueue] = (
        test_db_session.query(EmployeePushToFineosQueue)
        .filter(EmployeePushToFineosQueue.action == "UPDATE_NEW_EMPLOYER")
        .all()
    )
    assert len(employee_employer_logs) == 0
    # ------------


def test_record_employees_with_new_employers(test_db_session, monkeypatch):

    # Employee generate helper
    def generate_employee_and_wage_item(id, employer):
        employee = next(generator.generate_single_employee(id, [employer]))
        # convert quarter to date
        employee["filing_period"] = employee["filing_period"].as_date()
        return employee

    # Create two employers
    employer1 = generator.generate_single_employer(1)
    employer2 = generator.generate_single_employer(2)
    employers = [employer1, employer2]
    for employer in employers:
        employer["family_exemption"] = employer["medical_exemption"] = True
        employer["exemption_commence_date"] = datetime.date(2024, 1, 1)
        employer["exemption_cease_date"] = datetime.date(2024, 12, 31)

    report, report_log_entry = get_new_import_report(test_db_session)
    import_exempt_employers(test_db_session, employers, report, report_log_entry.import_log_id)

    created_employers = test_db_session.query(Employer).all()
    assert len(created_employers) == 2

    # Verify Employer Logs are correct (1)
    employer_insert_logs1: List[EmployerPushToFineosQueue] = (
        test_db_session.query(EmployerPushToFineosQueue)
        .filter(EmployerPushToFineosQueue.action == "INSERT")
        .all()
    )
    assert len(employer_insert_logs1) == 2

    found_employer_1 = next(
        x for x in employer_insert_logs1 if x.employer_id == created_employers[0].employer_id
    )
    assert found_employer_1

    found_employer_2 = next(
        x for x in employer_insert_logs1 if x.employer_id == created_employers[1].employer_id
    )
    assert found_employer_2

    # ------
    employer_update_logs1: List[EmployerPushToFineosQueue] = (
        test_db_session.query(EmployerPushToFineosQueue)
        .filter(EmployerPushToFineosQueue.action == "UPDATE")
        .all()
    )
    assert len(employer_update_logs1) == 0
    # ------------

    # Create two employees
    employee1 = generate_employee_and_wage_item(1, employer1)
    employee2 = generate_employee_and_wage_item(2, employer2)

    employee_ssns_to_id_created_in_current_import_run = {}

    report1, report_log_entry1 = get_new_import_report(test_db_session)
    wage_import1 = WageImporter(
        test_db_session,
        [employee1, employee2],
        employee_ssns_to_id_created_in_current_import_run,
        report1,
        list(),
        report_log_entry1.import_log_id,
        WagesAndContributionsDatasource.EXEMPTION_FILED,
    )
    wage_import1.import_employees_and_wage_data(is_exempt_import=True)

    assert report1.created_employees_count == 2
    assert report1.logged_employees_for_new_employer == 0

    created_employees = test_db_session.query(Employee).all()
    assert len(created_employees) == 2

    created_wages = test_db_session.query(WagesAndContributions).all()
    assert len(created_wages) == 2

    created_wage_histories = test_db_session.query(WagesAndContributionsHistory).all()
    assert len(created_wage_histories) == 2

    # Verify Employee Logs are correct (1)
    employee_insert_logs1: List[EmployeePushToFineosQueue] = (
        test_db_session.query(EmployeePushToFineosQueue)
        .filter(EmployeePushToFineosQueue.action == "INSERT")
        .all()
    )
    employee_insert_log_ids1 = [x.employee_push_to_fineos_queue_id for x in employee_insert_logs1]

    assert len(employee_insert_logs1) == 2

    found_employee_1 = next(
        x for x in employee_insert_logs1 if x.employee_id == created_employees[0].employee_id
    )
    assert found_employee_1

    found_employee_2 = next(
        x for x in employee_insert_logs1 if x.employee_id == created_employees[1].employee_id
    )
    assert found_employee_2
    # ------
    employee_update_logs1: List[EmployeePushToFineosQueue] = (
        test_db_session.query(EmployeePushToFineosQueue)
        .filter(EmployeePushToFineosQueue.action == "UPDATE")
        .all()
    )
    assert len(employee_update_logs1) == 0
    # ------
    employee_employer_logs1: List[EmployeePushToFineosQueue] = (
        test_db_session.query(EmployeePushToFineosQueue)
        .filter(EmployeePushToFineosQueue.action == "UPDATE_NEW_EMPLOYER")
        .all()
    )
    assert len(employee_employer_logs1) == 0
    # ------------

    # Simulate a wage entry for an existing employee with a new employer
    employee2_employer1 = generate_employee_and_wage_item(2, employer1)
    employee2_employer1_second_entry = generate_employee_and_wage_item(2, employer1)
    employee3 = generate_employee_and_wage_item(3, employer1)
    employee_ssns_to_id_created_in_current_import_run = {}

    report2, report_log_entry2 = get_new_import_report(test_db_session)
    wage_import2 = WageImporter(
        test_db_session,
        [
            employee1,
            employee2,
            employee2_employer1,
            employee2_employer1_second_entry,
            employee3,
        ],
        employee_ssns_to_id_created_in_current_import_run,
        report2,
        list(),
        report_log_entry2.import_log_id,
        WagesAndContributionsDatasource.EXEMPTION_FILED,
    )
    wage_import2.import_employees_and_wage_data(is_exempt_import=True)

    assert report2.unmodified_employees_count == 2
    assert report2.created_employees_count == 1
    assert report2.logged_employees_for_new_employer == 1

    created_wage_histories = test_db_session.query(WagesAndContributionsHistory).all()
    assert len(created_wage_histories) == 5

    employee_with_new_employer = dor_persistence_util.get_employees_by_ssn(
        test_db_session, [employee2_employer1["employee_ssn"]]
    )[0]

    created_employee = dor_persistence_util.get_employees_by_ssn(
        test_db_session, [employee3["employee_ssn"]]
    )[0]

    # Verify Employee Logs are correct (2)
    employee_insert_logs2: List[EmployeePushToFineosQueue] = (
        test_db_session.query(EmployeePushToFineosQueue)
        .filter(
            EmployeePushToFineosQueue.action == "INSERT",
            not_(
                EmployeePushToFineosQueue.employee_push_to_fineos_queue_id.in_(
                    employee_insert_log_ids1
                )
            ),
        )
        .all()
    )
    assert len(employee_insert_logs2) == 1
    assert employee_insert_logs2[0].employee_id == created_employee.employee_id
    # ------
    employee_update_logs2: List[EmployeePushToFineosQueue] = (
        test_db_session.query(EmployeePushToFineosQueue)
        .filter(EmployeePushToFineosQueue.action == "UPDATE")
        .all()
    )
    assert len(employee_update_logs2) == 0
    # ------
    employee_employer_logs2: List[EmployeePushToFineosQueue] = (
        test_db_session.query(EmployeePushToFineosQueue)
        .filter(EmployeePushToFineosQueue.action == "UPDATE_NEW_EMPLOYER")
        .all()
    )
    assert len(employee_employer_logs2) == 1
    assert employee_employer_logs2[0].employer_id == found_employer_1.employer_id
    assert employee_employer_logs2[0].employee_id == employee_with_new_employer.employee_id
    # ------------


def test_process_exempt_employer_files(test_db_session, test_fs_path):
    employer_file_path1 = "{}/{}".format(str(test_fs_path), exempt_employer_file)
    employer_file_path2 = "{}/{}".format(str(test_fs_path), exempt_employer_file2)

    import_files = list()
    import_files.append(str(employer_file_path1))
    import_files.append(str(employer_file_path2))

    reports = import_exempt_dor.process_exempt_employer_files(
        import_files=import_files,
        importer=import_exempt_dor.import_full,
        decrypt_files=False,
        db_session=test_db_session,
    )
    assert len(reports) == 2
    report_one = reports[0]
    assert report_one.created_employers_count == 1
    assert report_one.updated_employers_count == 0
    assert report_one.created_employees_count == 1
    assert report_one.status == "success"
    report_two = reports[1]
    assert report_two.created_employers_count == 0
    assert report_two.updated_employers_count == 1
    assert report_two.created_employees_count == 0
    assert report_one.status == "success"


def test_employee_file_name_parsing():
    for record in pending_filing_b_records:
        employee = EMPLOYER_PENDING_FILING_RESPONSE_FILE_FORMAT_B.parse_line(record)
        assert employee["fstrFirstName"] == "jo,hn"
        assert employee["fstrLastName"] == "O'hare"

    for record in b_records:
        employee = EMPLOYEE_FORMAT.parse_line(record)
        assert employee["employee_first_name"] == "Timothy"
        assert employee["employee_last_name"] == "O'CONN"


def test_record_employees_with_invalid_ssn_with_new_employers(test_db_session, monkeypatch):

    # Employee generate helper
    def generate_employee_and_wage_item(id, employer, with_invalid_ssn=False):
        employee = next(
            generator.generate_single_employee(id, [employer], with_invalid_ssn=with_invalid_ssn)
        )
        # convert quarter to date
        employee["filing_period"] = employee["filing_period"].as_date()
        return employee

    # Create two employers
    employer1 = generator.generate_single_employer(1)
    employer2 = generator.generate_single_employer(2)
    employers = [employer1, employer2]
    for employer in employers:
        employer["family_exemption"] = employer["medical_exemption"] = True
        employer["exemption_commence_date"] = datetime.date(2024, 1, 1)
        employer["exemption_cease_date"] = datetime.date(2024, 12, 31)

    report, report_log_entry = get_new_import_report(test_db_session)
    import_exempt_employers(test_db_session, employers, report, report_log_entry.import_log_id)

    created_employers = test_db_session.query(Employer).all()
    assert len(created_employers) == 2

    # Verify Employer Logs are correct (1)
    employer_insert_logs1: List[EmployerPushToFineosQueue] = (
        test_db_session.query(EmployerPushToFineosQueue)
        .filter(EmployerPushToFineosQueue.action == "INSERT")
        .all()
    )
    assert len(employer_insert_logs1) == 2

    found_employer_1 = next(
        x for x in employer_insert_logs1 if x.employer_id == created_employers[0].employer_id
    )
    assert found_employer_1

    found_employer_2 = next(
        x for x in employer_insert_logs1 if x.employer_id == created_employers[1].employer_id
    )
    assert found_employer_2

    # ------
    employer_update_logs1: List[EmployerPushToFineosQueue] = (
        test_db_session.query(EmployerPushToFineosQueue)
        .filter(EmployerPushToFineosQueue.action == "UPDATE")
        .all()
    )
    assert len(employer_update_logs1) == 0
    # ------------

    # Create two employees, 1 with invalid ssn
    employee1 = generate_employee_and_wage_item(1, employer1, True)
    employee2 = generate_employee_and_wage_item(2, employer2)

    employee_ssns_to_id_created_in_current_import_run = {}

    report1, report_log_entry1 = get_new_import_report(test_db_session)
    wage_import1 = WageImporter(
        test_db_session,
        [employee1, employee2],
        employee_ssns_to_id_created_in_current_import_run,
        report1,
        list(),
        report_log_entry1.import_log_id,
        WagesAndContributionsDatasource.EXEMPTION_FILED,
    )
    wage_import1.import_employees_and_wage_data(is_exempt_import=True)

    assert report1.created_employees_count == 1
    assert report1.skipped_employees_count == 1
    assert report1.logged_employees_for_new_employer == 0

    created_employees = test_db_session.query(Employee).all()
    assert len(created_employees) == 1

    created_wages = test_db_session.query(WagesAndContributions).all()
    assert len(created_wages) == 1

    created_wage_histories = test_db_session.query(WagesAndContributionsHistory).all()
    assert len(created_wage_histories) == 1

    # Verify Employee Logs are correct (1)
    employee_insert_logs1: List[EmployeePushToFineosQueue] = (
        test_db_session.query(EmployeePushToFineosQueue)
        .filter(EmployeePushToFineosQueue.action == "INSERT")
        .all()
    )
    employee_insert_log_ids1 = [x.employee_push_to_fineos_queue_id for x in employee_insert_logs1]

    assert len(employee_insert_logs1) == 1

    found_employee_1 = next(
        x for x in employee_insert_logs1 if x.employee_id == created_employees[0].employee_id
    )
    assert found_employee_1
    # ------
    employee_update_logs1: List[EmployeePushToFineosQueue] = (
        test_db_session.query(EmployeePushToFineosQueue)
        .filter(EmployeePushToFineosQueue.action == "UPDATE")
        .all()
    )
    assert len(employee_update_logs1) == 0
    # ------
    employee_employer_logs1: List[EmployeePushToFineosQueue] = (
        test_db_session.query(EmployeePushToFineosQueue)
        .filter(EmployeePushToFineosQueue.action == "UPDATE_NEW_EMPLOYER")
        .all()
    )
    assert len(employee_employer_logs1) == 0
    # ------------

    # Simulate a wage entry for an existing employee with a new employer
    employee2_employer1 = generate_employee_and_wage_item(2, employer1)
    employee2_employer1_second_entry = generate_employee_and_wage_item(2, employer1)
    employee3 = generate_employee_and_wage_item(3, employer1)
    employee_ssns_to_id_created_in_current_import_run = {}

    report2, report_log_entry2 = get_new_import_report(test_db_session)
    wage_import2 = WageImporter(
        test_db_session,
        [
            employee1,
            employee2,
            employee2_employer1,
            employee2_employer1_second_entry,
            employee3,
        ],
        employee_ssns_to_id_created_in_current_import_run,
        report2,
        list(),
        report_log_entry2.import_log_id,
        WagesAndContributionsDatasource.EXEMPTION_FILED,
    )
    wage_import2.import_employees_and_wage_data(is_exempt_import=True)

    assert report2.unmodified_employees_count == 1
    assert report2.created_employees_count == 1
    assert report2.logged_employees_for_new_employer == 1

    created_wage_histories = test_db_session.query(WagesAndContributionsHistory).all()
    assert len(created_wage_histories) == 4

    employee_with_new_employer = dor_persistence_util.get_employees_by_ssn(
        test_db_session, [employee2_employer1["employee_ssn"]]
    )[0]

    created_employee = dor_persistence_util.get_employees_by_ssn(
        test_db_session, [employee3["employee_ssn"]]
    )[0]

    # Verify Employee Logs are correct (2)
    employee_insert_logs2: List[EmployeePushToFineosQueue] = (
        test_db_session.query(EmployeePushToFineosQueue)
        .filter(
            EmployeePushToFineosQueue.action == "INSERT",
            not_(
                EmployeePushToFineosQueue.employee_push_to_fineos_queue_id.in_(
                    employee_insert_log_ids1
                )
            ),
        )
        .all()
    )
    assert len(employee_insert_logs2) == 1
    assert employee_insert_logs2[0].employee_id == created_employee.employee_id
    # ------
    employee_update_logs2: List[EmployeePushToFineosQueue] = (
        test_db_session.query(EmployeePushToFineosQueue)
        .filter(EmployeePushToFineosQueue.action == "UPDATE")
        .all()
    )
    assert len(employee_update_logs2) == 0
    # ------
    employee_employer_logs2: List[EmployeePushToFineosQueue] = (
        test_db_session.query(EmployeePushToFineosQueue)
        .filter(EmployeePushToFineosQueue.action == "UPDATE_NEW_EMPLOYER")
        .all()
    )
    assert len(employee_employer_logs2) == 1
    assert employee_employer_logs2[0].employer_id == found_employer_1.employer_id
    assert employee_employer_logs2[0].employee_id == employee_with_new_employer.employee_id
    # ------------


def test_import_employees_and_wage_data_uses_exemption_filed_datasource(
    test_db_session: Session,
):
    employer = generator.generate_single_employer(1)
    employer["exemption_cease_date"] = generator.NO_EXEMPTION_DATE
    employer["exemption_commence_date"] = generator.NO_EXEMPTION_DATE
    employer["family_exemption"] = False
    employer["medical_exemption"] = False
    employer_import_report, employer_import_log = get_new_import_report(test_db_session)
    import_employers(
        test_db_session, [employer], employer_import_report, employer_import_log.import_log_id
    )
    employee = next(generator.generate_single_employee(1, [employer]))
    employee["filing_period"] = employee["filing_period"].as_date()
    import_report, import_log = get_new_import_report(test_db_session)
    wage_import = WageImporter(
        test_db_session,
        [employee],
        {},
        import_report,
        [],
        import_log.import_log_id,
        WagesAndContributionsDatasource.EXEMPTION_FILED,
    )

    wage_import.import_employees_and_wage_data(is_exempt_import=True)

    wages_and_contributions = test_db_session.query(WagesAndContributions).all()
    assert len(wages_and_contributions) == 1

    for wages in wages_and_contributions:
        assert (
            wages.datasource.wages_and_contributions_datasource_id
            == WagesAndContributionsDatasource.EXEMPTION_FILED.wages_and_contributions_datasource_id
        )

    wages_and_contributions_histories = test_db_session.query(WagesAndContributionsHistory).all()
    assert len(wages_and_contributions_histories) == 1

    for history in wages_and_contributions_histories:
        assert (
            history.datasource.wages_and_contributions_datasource_id
            == WagesAndContributionsDatasource.EXEMPTION_FILED.wages_and_contributions_datasource_id
        )


def test_import_employees_and_wage_data_does_not_overwrite_dfml_wages_and_contributions(
    initialize_factories_session, test_db_session: Session
):
    filing_period = datetime.date(2024, 12, 31)

    existing_boolean = False
    existing_datasource = WagesAndContributionsDatasource.DFML_REPORTED_WAGES
    existing_wage_amount = Decimal("1.00")
    existing_employee, existing_employer, existing_wages_and_contributions = (
        test_data.generate_existing_entities_for_wage_and_contributions_testing(
            existing_boolean,
            existing_datasource,
            filing_period,
            existing_wage_amount,
            existing_wage_amount,
        )
    )

    new_boolean = True
    new_wage_amount = Decimal("2.00")
    new_wage_data = test_data.generate_new_wage_data_for_wages_and_contributions_testing(
        new_boolean, existing_employee, existing_employer, filing_period, new_wage_amount
    )
    new_datasource = WagesAndContributionsDatasource.EXEMPTION_FILED

    import_report, import_log = get_new_import_report(test_db_session)

    wage_import = WageImporter(
        test_db_session,
        [new_wage_data],
        {},
        import_report,
        [],
        import_log.import_log_id,
        new_datasource,
    )

    wage_import.import_employees_and_wage_data(is_exempt_import=True)

    # No values have changed.
    wages_and_contributions_records = test_db_session.query(WagesAndContributions).all()
    assert len(wages_and_contributions_records) == 1
    wages_and_contributions = wages_and_contributions_records[0]
    assert (
        wages_and_contributions.wages_and_contributions_datasource_id
        == existing_datasource.wages_and_contributions_datasource_id
    )
    assert wages_and_contributions.employee_fam_contribution == existing_wage_amount
    assert wages_and_contributions.employee_med_contribution == existing_wage_amount
    assert wages_and_contributions.employee_qtr_wages == existing_wage_amount
    assert wages_and_contributions.employee_ytd_wages == existing_wage_amount
    assert wages_and_contributions.employer_fam_contribution == existing_wage_amount
    assert wages_and_contributions.employer_med_contribution == existing_wage_amount
    assert wages_and_contributions.filing_period == filing_period
    assert wages_and_contributions.is_independent_contractor == existing_boolean
    assert wages_and_contributions.is_opted_in == existing_boolean

    # The existing history has not changed.
    wages_and_contributions_history_records = test_db_session.query(
        WagesAndContributionsHistory
    ).all()
    assert len(wages_and_contributions_history_records) == 1
    wages_and_contributions_history = wages_and_contributions_history_records[0]
    assert (
        wages_and_contributions_history.wages_and_contributions_datasource_id
        == existing_datasource.wages_and_contributions_datasource_id
    )
    assert wages_and_contributions_history.employee_fam_contribution == existing_wage_amount
    assert wages_and_contributions_history.employee_med_contribution == existing_wage_amount
    assert wages_and_contributions_history.employee_qtr_wages == existing_wage_amount
    assert wages_and_contributions_history.employee_ytd_wages == existing_wage_amount
    assert wages_and_contributions_history.employer_fam_contribution == existing_wage_amount
    assert wages_and_contributions_history.employer_med_contribution == existing_wage_amount
    assert wages_and_contributions_history.is_independent_contractor == existing_boolean
    assert wages_and_contributions_history.is_opted_in == existing_boolean

    # A difference in quarterly wages was detected, but no updates were made.
    assert import_report.differing_qtr_wages_count == 1

    # Unmodified count is not tallied when run with should_update_wages set False.
    assert import_report.unmodified_wages_and_contributions_count == 0
    assert import_report.updated_wages_and_contributions_count == 0

    # No WagesAndContributionsUnused should be created
    wages_and_contributions_unused_records = test_db_session.query(
        WagesAndContributionsUnused
    ).all()

    assert len(wages_and_contributions_unused_records) == 0
