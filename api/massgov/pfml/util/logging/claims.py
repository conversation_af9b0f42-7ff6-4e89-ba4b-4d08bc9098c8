from collections import defaultdict
from datetime import date
from typing import Any, Dict, List, Optional

import massgov.pfml.util.logging
from massgov.pfml.api.models.claims.common import EmployerClaimReview
from massgov.pfml.api.models.claims.responses import (
    ClaimResponse,
    DetailedClaimResponse,
    ManagedRequirementResponse,
)
from massgov.pfml.db.models.employees import Claim
from massgov.pfml.util.logging.absence_periods import log_absence_period_response

logger = massgov.pfml.util.logging.get_logger(__name__)


def get_claim_log_attributes(claim: Optional[Claim]) -> Dict[str, Any]:
    log_attr: Dict[str, Any] = {}

    if claim is None:
        return log_attr

    application = claim.application
    if application is None:
        return log_attr
    log_attr.update(
        {
            "application_started_at": application.submitted_time,
            "application_completed_at": application.ready_for_review_time,
        }
    )

    appeal = application.appeal
    if appeal is not None:
        log_attr.update({"appleal_filed": True})

    leave_reason = (
        application.leave_reason.leave_reason_description if application.leave_reason else None
    )
    log_attr.update({"leave_reason": leave_reason})

    if claim.claim_type_description is not None:
        log_attr.update({"claim_type": claim.claim_type_description})

    return log_attr


def get_managed_requirements_log_attributes(
    managed_requirements: List[ManagedRequirementResponse],
) -> Dict[str, Any]:
    responded_requirements = list(
        filter(lambda r: r.responded_at is not None, managed_requirements)
    )

    return {
        "num_requirements": len(managed_requirements),
        "num_responded_requirements": len(responded_requirements),
    }


def get_claim_review_log_attributes(claim_review: Optional[EmployerClaimReview]) -> Dict[str, Any]:
    if claim_review is None:
        return {}

    relationship_accurate_val = (
        claim_review.believe_relationship_accurate.value
        if claim_review.believe_relationship_accurate
        else None
    )

    return {
        "claim_request.believe_relationship_accurate": relationship_accurate_val,
        "claim_request.employer_decision": claim_review.employer_decision,
        "claim_request.fraud": claim_review.fraud,
        "claim_request.has_amendments": claim_review.has_amendments,
        "claim_request.has_comment": str(bool(claim_review.comment)),
        "claim_request.num_previous_leaves": len(claim_review.previous_leaves),
        "claim_request.num_employer_benefits": len(claim_review.employer_benefits),
    }


def _get_claim_change_request_log_attributes(claim: Claim) -> Dict[str, Any]:
    change_requests = claim.change_requests if claim.change_requests else []

    return {"num_change_requests": len(change_requests)}


def _get_intermittent_leave_episodes_log_attributes(
    claim_detail: DetailedClaimResponse,
) -> Dict[str, Any]:
    intermittent_leave_episodes = (
        claim_detail.intermittent_leave_episodes if claim_detail.intermittent_leave_episodes else []
    )

    return {
        "num_intermittent_leave_episodes": len(intermittent_leave_episodes),
    }


# Note: Pass both a Claim and a DetailedClaimResponse because there are fields on Claim that aren't yet present on DetailedClaimResponse
def log_get_claim_metrics(claim: Claim, claim_detail: DetailedClaimResponse) -> None:
    periods = claim_detail.absence_periods if claim_detail.absence_periods is not None else []
    period_dict = defaultdict(list)

    for period in periods:
        period_dict[period.request_decision].append(period)

        # log individual absence period info as well
        log_absence_period_response(
            claim_detail.fineos_absence_id, period, "get_claim - Found absence period for claim"
        )

    approved_periods = period_dict["Approved"]
    denied_periods = period_dict["Denied"]
    pending_periods = period_dict["Pending"]

    log_attributes: Dict[str, Any] = {
        "absence_id": claim_detail.fineos_absence_id,
        "absence_case_id": claim_detail.fineos_absence_id,
        "num_absence_periods": len(periods),
        "num_approved_absence_periods": len(approved_periods),
        "num_denied_absence_periods": len(denied_periods),
        "num_pending_absence_periods": len(pending_periods),
    }

    # if we have pending absence periods, add logs for the managed requirements
    if len(pending_periods) >= 1:
        requirements = claim_detail.managed_requirements

        if requirements is None or len(requirements) == 0:
            logger.warning(
                "get_claim - No managed requirements found for claim with pending absence period",
                extra={
                    "absence_id": claim_detail.fineos_absence_id,
                    "absence_case_id": claim_detail.fineos_absence_id,
                },
            )
        else:
            open_requirements = list(filter(lambda r: r.status == "Open", requirements))

            if len(open_requirements) == 0:
                pass  # no open requirements - nothing to log
            elif len(open_requirements) == 1:
                requirement = open_requirements[0]
                log_attributes.update({"employer_follow_up_date": requirement.follow_up_date})
            else:
                logger.warning(
                    "get_claim - Multiple open requirements for claim",
                    extra={
                        "absence_id": claim_detail.fineos_absence_id,
                        "absence_case_id": claim_detail.fineos_absence_id,
                    },
                )

    log_attributes.update(_get_claim_change_request_log_attributes(claim))
    log_attributes.update(get_claim_log_attributes(claim))
    log_attributes.update(_get_intermittent_leave_episodes_log_attributes(claim_detail))

    logger.info("get_claim success", extra=log_attributes)


def log_managed_requirement(
    managed_requirement: ManagedRequirementResponse, fineos_absence_id: str
) -> None:
    log_attributes: Dict[str, Any] = {
        "absence_case_id": fineos_absence_id,
        "follow_up_date": managed_requirement.follow_up_date,
        "responded_at": managed_requirement.responded_at,
        "status": managed_requirement.status,
        "category": managed_requirement.category,
        "type": managed_requirement.type,
        "dfml_decision_date": managed_requirement.dfml_decision_date,
        "responded_user_first_name": managed_requirement.responded_user_first_name,
        "responded_user_last_name": managed_requirement.responded_user_last_name,
    }

    if managed_requirement.status == "Open" and managed_requirement.follow_up_date is not None:
        current_date = date.today()
        days_to_follow_up_date = managed_requirement.follow_up_date - current_date
        log_attributes.update({"days_to_follow_up_date": days_to_follow_up_date.days})

    if managed_requirement.dfml_decision_date is not None:
        days_to_dfml_decision_date = managed_requirement.dfml_decision_date - date.today()
        log_attributes.update({"days_to_dfml_decision_date": days_to_dfml_decision_date.days})

    logger.info("Found managed requirement for claim", extra=log_attributes)


def get_claims_search_results_info(results: List[Any]) -> Dict[str, Any]:
    # determine how many claims are in the result
    # (related to but distinct from page.total_records and request.paging.size)
    claims = [result for result in results if type(result) is Claim]

    # determine how many claims require a payment sub-query calculation
    claims_that_need_payment_calculation = [
        claim for claim in claims if claim.needs_payment_calculation
    ]

    response_keys: Dict[str, Any] = {
        "num_claims": len(claims),
        "num_payment_subqueries": len(claims_that_need_payment_calculation),
    }

    return response_keys


def get_log_attributes_from_search_info(results: List[Any]) -> Dict[str, Any]:

    # determine if there is null dates in the claim response vs dates available in the application
    total_claims = 0
    claims_with_started_date = 0
    claims_with_completed_date = 0
    claims_with_dfml_date = 0

    for result in results:
        if not isinstance(result, ClaimResponse):
            continue

        total_claims += 1

        if result.application_started_at_date is not None:
            claims_with_started_date += 1

        if result.application_completed_at_date is not None:
            claims_with_completed_date += 1

        if result.managed_requirements and any(
            mr.dfml_decision_date is not None for mr in result.managed_requirements
        ):
            claims_with_dfml_date += 1

    log_attributes: Dict[str, Any] = {
        "num_claims": total_claims,
        "num_claims_with_application_started_date": claims_with_started_date,
        "num_claims_with_application_completed_date": claims_with_completed_date,
        "num_claims_with_null_application_started_date": total_claims - claims_with_started_date,
        "num_claims_with_null_application_completed_date": total_claims
        - claims_with_completed_date,
        "num_claims_with_dfml_decision_date": claims_with_dfml_date,
        "num_claims_with_null_dfml_decision_date": total_claims - claims_with_dfml_date,
    }

    return log_attributes
