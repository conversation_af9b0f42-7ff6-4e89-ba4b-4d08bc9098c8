#!/usr/bin/python3
#
# ECS task to import DOR data from S3 to PostgreSQL (RDS).
#

import sys
from datetime import datetime
from typing import Callable, List, Optional

import boto3
from sqlalchemy.orm.session import Session

import massgov.pfml.db
import massgov.pfml.util.logging as logging
from massgov.pfml.db.lookup_data.employees import WagesAndContributionsDatasource
from massgov.pfml.dor.importer.dor_file_formats import ParsedEmployeeLine, ParsedEmployerLine
from massgov.pfml.dor.importer.dor_handler_utils import run_import
from massgov.pfml.dor.importer.dor_shared_utils import (
    ImportException,
    ImportFiles,
    ImportReport,
    handle_import_exception,
    process_dor_files,
)
from massgov.pfml.dor.importer.employers.employer_importer import import_exempt_employers
from massgov.pfml.dor.importer.import_wages import WageImporter
from massgov.pfml.dor.util.process_dor_import import DORImport, ProcessDORImport
from massgov.pfml.util.bg import background_task
from massgov.pfml.util.encryption import Crypt

logger = logging.get_logger("massgov.pfml.dor.importer.import_exempt_dor")

# If an employer has a date of '12/31/9999' in the extract then the employer has no exemptions.
EXEMPTION_DATE = datetime.strptime("12/31/9999", "%m/%d/%Y").date()

ImporterFunction = Callable[
    [Session, List[ParsedEmployeeLine], List[ParsedEmployerLine], ImportReport, int],
    ImportReport,
]


def import_full(
    db_session: Session,
    employees: List[ParsedEmployeeLine],
    employers: List[ParsedEmployerLine],
    report: ImportReport,
    import_log_entry_id: int,
) -> ImportReport:
    import_exempt_employers(db_session, employers, report, import_log_entry_id)
    wage_import = WageImporter(
        db_session,
        employees,
        dict(),
        report,
        list(),
        import_log_entry_id,
        WagesAndContributionsDatasource.EXEMPTION_FILED,
    )
    wage_import.import_employees_and_wage_data(is_exempt_import=True)
    return report


@background_task("dor-import-exempt")
def handler() -> None:
    """ECS task main method."""
    try:
        run_import(
            ImportFiles(file_type="exempt", importer=import_full),
            massgov.pfml.db.init(sync_lookups=True),
        )
    except Exception:
        sys.exit(1)


def process_exempt_employer_files(
    import_files: List[str],
    decrypt_files: bool,
    importer: ImporterFunction,
    db_session: Session,
    optional_decrypter: Optional[Crypt] = None,
    optional_s3: Optional[boto3.Session] = None,
    repush: bool = False,
) -> List[ImportReport]:
    try:
        dor_files = ImportFiles(
            import_files=import_files,
            decrypt_files=decrypt_files,
            decrypter=optional_decrypter,
            repush=repush,
            importer=importer,
            file_type="exempt",
        )
        import_reports: List[ImportReport] = process_dor_files(dor_files, db_session, optional_s3)

    except ImportException as ie:
        raise ie
    except Exception as e:
        raise ImportException("Unexpected error importing batches", type(e).__name__)

    return import_reports


def process_exempt_employer_import(
    db_session: Session,
    employer_file_path: str,
    decrypter: Crypt,
    s3: boto3.Session,
    importer: ImporterFunction,
    repush: bool = False,
) -> ImportReport:
    logger.info("Starting to process files")

    dor_import = DORImport(
        employer_file_path=employer_file_path,
        decrypter=decrypter,
        report_log_source="DOR_EXEMPT_REPUSH" if repush else "DOR_EXEMPT",
        report_log_import_type="Exempt Employers Repush" if repush else "Exempt Employers",
    )
    process_dor_import = ProcessDORImport(dor_import)
    dor_import.report_log_entry = process_dor_import.create_report_log(db_session)

    try:
        # If an employer file is given, parse and import
        if employer_file_path:
            employers, employees = process_dor_import.parse_exempt_employer_file(db_session)
            dor_import.parsed_employers_count = len(employers)
            dor_import.parsed_employees_count = len(employees)

            importer(
                db_session,
                employees,
                employers,
                dor_import.report,
                dor_import.report_log_entry.import_log_id,
            )

        # finalize report
        process_dor_import.finalize_report_log(db_session)

        logger.info(
            "Sample Employer line lengths: %s",
            repr(dor_import.report.sample_employers_line_lengths),
        )

        # move file to processed folder unless explicitly told not to.
        process_dor_import.move_employer_file(s3)

    except Exception as e:
        handle_import_exception(e, db_session, dor_import.report_log_entry, dor_import.report)

    return dor_import.report


if __name__ == "__main__":
    handler()
