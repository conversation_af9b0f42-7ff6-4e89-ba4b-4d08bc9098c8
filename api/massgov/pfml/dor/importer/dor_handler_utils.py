import os
from dataclasses import asdict, dataclass, field
from datetime import datetime
from typing import Any, List, Optional

import sqlalchemy.orm

import massgov.pfml.util.logging as logging
from massgov.pfml.dor import importer as importer_util
from massgov.pfml.dor.importer.dor_shared_utils import ImportException, ImportFiles, ImportReport
from massgov.pfml.dor.importer.paths import (
    get_exempt_employer_files_to_process,
    get_exemption_file_to_process,
    get_files_to_process,
    get_pending_filing_files_to_process,
)
from massgov.pfml.util.logging.filter import filter_add_memory_usage

logger = logging.get_logger(__name__)


@dataclass
class ImportRunReport:
    start: str
    imports: List[ImportReport] = field(default_factory=list)
    end: Optional[str] = None
    message: str = ""


def is_dor_child_support_email_notification_enabled() -> bool:
    """
    Check if the DOR child support email notification is enabled.
    This is controlled by the environment variable `DOR_CHILD_SUPPORT_EMAIL_NOTIFICATION`.
    """
    return os.getenv("DOR_CHILD_SUPPORT_EMAIL_NOTIFICATION", "0").lower() == "1"


def run_import(dor_import_files: ImportFiles, db_session: sqlalchemy.orm.Session) -> None:
    logger.addFilter(filter_add_memory_usage)

    report = ImportRunReport(start=datetime.now().isoformat())

    try:
        dor_import_files.decrypt_files = os.getenv("DECRYPT") == "true"

        import_files = get_import_files(dor_import_files)

        if not import_files:
            logger.info("no files found to import")
            report.message = "no files found to import"
        else:
            import_reports: List[ImportReport] = []
            if dor_import_files.file_type.lower() == "initial":
                import_reports = importer_util.process_import_batches(
                    import_files, dor_import_files.decrypt_files, db_session=db_session
                )
            if dor_import_files.file_type.lower() == "pending_filing":
                csv_folder_path = os.environ["CSV_FOLDER_PATH"]
                exemption_file = get_exemption_file_to_process(csv_folder_path)
                import_reports = importer_util.process_pending_filing_employer_files(
                    import_files,
                    dor_import_files.decrypt_files,
                    exemption_file,
                    db_session=db_session,
                )
            if dor_import_files.file_type.lower() == "exempt":
                import_reports = importer_util.process_exempt_employer_files(
                    import_files,
                    dor_import_files.decrypt_files,
                    dor_import_files.importer,
                    db_session=db_session,
                )

            report.imports = import_reports
            report.message = "files imported"

        report.end = datetime.now().isoformat()
        logger.info("Finished import run", extra={"report": asdict(report)})
    except ImportException as ie:
        report.end = datetime.now().isoformat()
        message = str(ie)
        report.message = message
        logger.error(message, extra={"report": asdict(report)})
    except Exception as e:
        report.end = datetime.now().isoformat()
        message = f"Unexpected error during import run: {type(e)} -- {e}"
        report.message = message
        logger.error(message, extra={"report": asdict(report)})


def get_import_files(
    dor_import_files: ImportFiles,
) -> Any:

    folder_path = os.environ["FOLDER_PATH"]

    logger.info(
        f"Starting import run {folder_path}",
        extra={"folder_path": folder_path, "decrypt_files": dor_import_files.decrypt_files},
    )

    if dor_import_files.file_type.lower() == "initial":
        return get_files_to_process(folder_path)
    if dor_import_files.file_type.lower() == "pending_filing":
        return get_pending_filing_files_to_process(folder_path)
    if dor_import_files.file_type.lower() == "exempt":
        return get_exempt_employer_files_to_process(folder_path)

    return None
