"""
Shared module used in many dor extract tasks. eg: `pending_filing_response`, `import_exempt_dor`, and `import_dor`.
"""

import os
import re
import uuid
from collections import Counter
from dataclasses import dataclass, field
from datetime import date, datetime
from typing import Any, Callable, Dict, List, Literal, Optional
from uuid import UUID

import boto3
import botocore
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm.session import Session

import massgov.pfml.dor.importer.dor_extract_config as dor_config
import massgov.pfml.util.files as file_util
import massgov.pfml.util.logging as logging
from massgov.pfml import db
from massgov.pfml.db.bulk import bulk_save
from massgov.pfml.db.models.employees import Employee, EmployeePushToFineosQueue
from massgov.pfml.db.models.import_log import ImportLog
from massgov.pfml.dor import importer as importer_util
from massgov.pfml.dor.importer.dor_file_formats import (
    ParsedEmployeeLine,
    ParsedEmployerLine,
    WageKey,
)
from massgov.pfml.dor.importer.lib import dor_persistence_util
from massgov.pfml.dor.importer.paths import ImportBatch
from massgov.pfml.util.aws.ses import EmailRecipient, send_email
from massgov.pfml.util.batch.log import update_log_entry
from massgov.pfml.util.datetime import get_now_us_eastern
from massgov.pfml.util.encryption import Crypt, decrypter_factory

logger = logging.get_logger(__name__)

# TODO get these from environment variables
RECEIVED_FOLDER = "dor/received/"
PROCESSED_FOLDER = "dor/processed/"
DFML_PROCESSED_FOLDER = "dfml/processed/"

EMPLOYEE_LINE_LIMIT = 25000


@dataclass
class ImportReport:
    start: str = datetime.now().isoformat()
    employer_file: str = ""
    employee_file: str = ""
    parsed_employers_count: int = 0
    parsed_employees_info_count: int = 0
    created_employers_count: int = 0
    updated_employers_count: int = 0
    unmodified_employers_count: int = 0
    created_employees_count: int = 0
    updated_employees_count: int = 0
    skipped_employers: list[ParsedEmployerLine] = field(default_factory=list)
    skipped_employers_count: int = 0
    skipped_employees_count: int = 0
    unmodified_employees_count: int = 0
    logged_employees_for_new_employer: int = 0
    created_wages_and_contributions_count: int = 0
    updated_wages_and_contributions_count: int = 0
    unmodified_wages_and_contributions_count: int = 0
    created_employer_quarters_count: int = 0
    updated_employer_quarters_count: int = 0
    skipped_employer_quarterly_contribution: int = 0
    unmodified_employer_quarters_count: int = 0
    sample_employers_line_lengths: dict[Any, Any] = field(default_factory=dict)
    invalid_employer_lines_count: int = 0
    parsed_employers_exception_line_nums: list[Any] = field(default_factory=list)
    invalid_employer_addresses_by_account_key: dict[Any, Any] = field(default_factory=dict)
    invalid_employer_feins_by_account_key: dict[Any, Any] = field(default_factory=dict)
    errored_employers_fein_count: int = 0
    invalid_employee_lines_count: int = 0
    skipped_wages_count: int = 0
    parsed_employer_quarter_exception_count: int = 0
    parsed_employees_exception_count: int = 0
    parsed_employee_wage_ids_with_exception: list[int] = field(default_factory=list)
    exception_count: Counter = field(default_factory=Counter)
    message: str = ""
    status: str | None = None
    end: str | None = None
    new_exemption_activities: int = 0
    updated_exemption_activities: int = 0
    unchanged_exemption_activities: int = 0
    duplicate_exemptions: int = 0
    employer_with_mtc_number: int = 0
    employers_with_unavailable_mtc_number_count: int = 0
    differing_qtr_wages_count: int = 0


@dataclass
class ImportFiles:
    decrypter: Optional[Crypt] = None
    decrypt_files: bool = False
    exemption_file_path: str = ""
    repush: bool = False
    import_files: List[str] = field(default_factory=list)
    import_batches: List[ImportBatch] = field(default_factory=list)
    importer: Callable = field(default=lambda _: _)
    file_type: Literal["exempt", "pending_filing", "initial"] = "initial"


@dataclass
class EmployeeReportNameChanges:
    dor_employee_data_integrity_changes: List[Dict[str, Any]] = field(default_factory=list)


class Constants:
    # yyyy-MM-dd-HH-mm-ss-dor-employee-integrity-report.csv
    EMPLOYEE_INTEGRITY_FILE_NAME = "dor-employee-integrity-report"
    EMPLOYEE_INTEGRITY_FOLDER_PATH = "dor-report"

    EMPLOYEE_SSN = "EmployeeSSN"
    DIFFERENCE_MEASURE = "DifferenceMeasure"
    PFML_FIRST_NAME = "CurrentPfmlFirstNameValue"
    PFML_LAST_NAME = "CurrentPfmlLastNameValue"
    DOR_FIRST_NAME = "NewFirstNameValue"
    DOR_LAST_NAME = "NewLastNameValue"

    EMPLOYEE_INTEGRITY_FILE_FIELDS = [
        EMPLOYEE_SSN,
        DIFFERENCE_MEASURE,
        PFML_FIRST_NAME,
        PFML_LAST_NAME,
        DOR_FIRST_NAME,
        DOR_LAST_NAME,
    ]


class ImportException(Exception):
    __slots__ = ["message", "error_type"]

    def __init__(self, message: str, error_type: str):
        self.message = message
        self.error_type = error_type


class Capturer(object):
    def __init__(self, line_offset: int):
        self.line_offset: int = line_offset
        self.line_count: int = 0

        self.remainder: str = ""
        self.remainder_encoded: bytes = b""
        self.lines: List = []

        logger.info("Capturer initialized")

    def append_line(self, line: str) -> None:
        if self.line_count < self.line_offset:
            self.line_count = self.line_count + 1
            return

        self.lines.append(line)
        self.line_count = self.line_count + 1

    def __call__(self, data: bytes) -> bool:
        if data:
            try:
                to_decode = self.remainder_encoded + data
                lines = str(to_decode.decode("utf-8")).split("\n")
                self.remainder_encoded = b""

                i = 0
                while i < len(lines):
                    if i != (len(lines) - 1):
                        self.append_line(self.remainder + lines[i])
                        self.remainder = ""
                    if i == (len(lines) - 1):
                        self.remainder = lines[i]

                    i += 1
            # We may have hit a UTF-8 boundary at the wrong byte. If so, save line for next data call
            except UnicodeDecodeError:
                self.remainder_encoded = data

        else:
            logger.info("Done parsing employer file", extra={"lines_parsed": self.line_count})

        return False


def get_decrypted_file_stream(file_path: str, decrypter: Crypt) -> Any:
    file_stream = file_util.open_stream(file_path, "rb")
    logger.info("Finished getting file stream")

    decrypt_files = os.getenv("DECRYPT") == "true"
    if decrypt_files:
        decrypter.decrypt_stream(file_stream)

        logger.info("Finished decrypted file", extra={"file path": file_path})
    else:
        return file_stream


def get_wage_composite_key(
    employer_id: uuid.UUID, employee_id: uuid.UUID, filing_period: date
) -> WageKey:
    return employer_id, employee_id, filing_period


def handle_import_exception(
    exception: Exception, db_session: Session, report_log_entry: ImportLog, report: ImportReport
) -> None:
    """Gracefully close out import run"""
    try:
        if isinstance(exception, SQLAlchemyError):
            db_session.rollback()
            message = get_discreet_db_exception_message(exception)
            logger.error(message)
        else:
            message = f"Unexpected error while processing import: {str(exception)}"
            logger.exception("exception occurred during import")

        report.status = "error"
        report.message = message
        report.end = datetime.now().isoformat()
        update_log_entry(db_session, report_log_entry, "error", report)
        import_exception = ImportException(message, type(exception).__name__)
    except Exception as e:
        message = f"Unexpected error while closing out import run due to original exception: {type(exception)}"
        import_exception = ImportException(message, type(e).__name__)
    finally:
        raise import_exception


# TODO take this out once psql is on TERSE logging setting https://lwd.atlassian.net/browse/API-697
def get_discreet_db_exception_message(db_exception: SQLAlchemyError) -> str:
    exception_type = type(db_exception).__name__
    # see https://github.com/zzzeek/sqlalchemy/blob/master/lib/sqlalchemy/exc.py
    message = db_exception._message()
    discreet_message = message
    detail_index = message.find("DETAIL")
    if detail_index != -1:
        discreet_message = message[0:detail_index]

    return f"DB Exception: {discreet_message}, exception type: {exception_type}"


def is_valid_employer_address(employer_info: ParsedEmployerLine, report: ImportReport) -> bool:
    try:
        dor_persistence_util.employer_dict_to_country_and_state_values(employer_info)
    except KeyError:
        invalid_address_msg = "city: {}, state: {}, zip: {}, country: {}".format(
            employer_info["employer_address_city"],
            employer_info["employer_address_state"],
            employer_info["employer_address_zip"],
            employer_info["employer_address_country"],
        )
        logger.warning(f"Invalid employer address - {invalid_address_msg}")
        report.invalid_employer_addresses_by_account_key[employer_info["account_key"]] = (
            invalid_address_msg
        )
        return False

    return True


RE_TAX_ID = re.compile(r"^[0-9]{9}$")


def is_valid_employee_tax_id(employee_info: ParsedEmployeeLine, report: ImportReport) -> bool:
    tax_id = str(employee_info.get("employee_ssn"))
    correct = RE_TAX_ID.fullmatch(tax_id) is not None
    if correct:
        return True
    report.invalid_employee_lines_count += 1
    logger.warning("Invalid employee tax_id")
    return False


def is_valid_employer_fein(employer_info: ParsedEmployerLine, report: ImportReport) -> bool:
    fein = str(employer_info.get("fein"))
    correct = RE_TAX_ID.fullmatch(fein) is not None
    if correct:
        return True
    err_msg = f"Invalid FEIN: {fein}. Expected a 9-digit integer value"
    report.errored_employers_fein_count += 1
    report.invalid_employer_feins_by_account_key[employer_info.get("account_key")] = err_msg
    logger.warning(f"Invalid employer fein - {fein}")
    return False


def is_valid_employer_mtc(employer_info: ParsedEmployerLine, report: ImportReport) -> bool:
    mtc_number = employer_info.get("mtc_number")

    if mtc_number is None or len(mtc_number) != 14:
        logger.warning(
            "Invalid/missing employer mtc encountered",
            extra={
                "account_key": employer_info["account_key"],
                "mtc_number": mtc_number,
            },
        )
        return False

    report.employer_with_mtc_number += 1
    return True


def is_employer_mtc_available(
    employer_info: ParsedEmployerLine, mtc_to_fein: dict[str, str], report: ImportReport
) -> bool:
    mtc_number = employer_info.get("mtc_number")
    fein = employer_info.get("fein")
    if not mtc_number or not fein or not isinstance(fein, str):
        return True

    fein_matching_mtc = mtc_to_fein.get(mtc_number)

    if fein_matching_mtc is not None and fein_matching_mtc != fein:
        logger.warning(
            "MTC is already associated with another employer",
            extra={
                "account_key": employer_info["account_key"],
                "mtc_number": mtc_number,
            },
        )
        report.employers_with_unavailable_mtc_number_count += 1
        return False
    return True


def move_file_to_processed(
    folder: str, file_path: str, s3_client: botocore.client.BaseClient
) -> None:
    """Move file from received to processed folder"""
    file_name = file_util.get_file_name(file_path)
    file_key = file_util.get_s3_file_key(file_path)

    bucket_name = file_util.get_s3_bucket(file_path)

    logger.info("Moving file to processed folder. Bucket: %s, file: %s", bucket_name, file_key)

    file_dest_key = folder + file_name

    s3_client.copy({"Bucket": bucket_name, "Key": file_key}, bucket_name, file_dest_key)
    s3_client.delete_object(Bucket=bucket_name, Key=file_key)


def record_employee_employer_pairs(
    db_session: Session,
    employee_wage_info_list: List,
    employer_fein_to_employer_id_map: Dict[str, uuid.UUID],
    ssn_to_existing_employee_ids: Dict[str, uuid.UUID],
    report: ImportReport,
    push_filter: Optional[Callable[[uuid.UUID, uuid.UUID], bool]] = None,
) -> int:
    logger.info("Logging employee-employer pairs")

    # Check current list for new employers
    # Filter current list to existing employees only.
    # New employees will have an insert log already, so no need to check here.
    # Note: duplicate entries for the same employer may be created across batches
    employee_wage_info_for_existing_employees = list(
        filter(
            lambda employee_wage_info: employee_wage_info["employee_ssn"]
            in ssn_to_existing_employee_ids,
            employee_wage_info_list,
        )
    )

    push_to_fineos_queue_items_to_create = []
    already_logged_employee_id_employer_id_tuples = set()

    for employee_wage_info in employee_wage_info_for_existing_employees:
        account_key = employee_wage_info["account_key"]
        employee_id = ssn_to_existing_employee_ids[employee_wage_info["employee_ssn"]]
        employer_id = employer_fein_to_employer_id_map.get(
            employee_wage_info["employer_fein"], None
        )

        if employer_id is None:
            logger.warning(
                "Attempted to check an employee wage row for unknown employer: %s",
                account_key,
                extra={"account_key": account_key},
            )
            continue

        if (employee_id, employer_id) in already_logged_employee_id_employer_id_tuples:
            continue

        if not push_filter or push_filter(employee_id, employer_id):
            push_to_fineos_queue_item = EmployeePushToFineosQueue(
                employee_id=employee_id, employer_id=employer_id, action="UPDATE_NEW_EMPLOYER"
            )
            logger.info(
                "Added existing employee with new employer to the queue",
                extra={
                    "employee_id": employee_id,
                    "employer_id": employer_id,
                    "action": "UPDATE_NEW_EMPLOYER",
                },
            )
            push_to_fineos_queue_items_to_create.append(push_to_fineos_queue_item)
            already_logged_employee_id_employer_id_tuples.add((employee_id, employer_id))

    push_to_fineos_queue_items_count = len(push_to_fineos_queue_items_to_create)
    if push_to_fineos_queue_items_count > 0:
        logger.info(
            "Logging employees as updated for new employer: %i",
            push_to_fineos_queue_items_count,
        )
        bulk_save(
            db_session,
            push_to_fineos_queue_items_to_create,
            "Employee Logs (New Employer Update)",
            commit=True,
        )

    report.logged_employees_for_new_employer += push_to_fineos_queue_items_count
    return push_to_fineos_queue_items_count


def create_employer_fein_to_existing_employer_map(
    db_session: Session, employee_and_wage_info_list: List[ParsedEmployeeLine]
) -> dict[str, uuid.UUID]:
    employer_feins = {
        employee_info["employer_fein"] for employee_info in employee_and_wage_info_list
    }
    employer_fein_to_employer_id_map = dor_persistence_util.get_employers_by_employer_fein(
        db_session, employer_feins
    )
    return employer_fein_to_employer_id_map


def create_existing_employee_reference_maps_with_employee_models(
    db_session: Session,
    employee_and_wage_info_list: List[ParsedEmployeeLine],
    employee_ssns_created_in_current_import_run: Dict[str, uuid.UUID],
) -> tuple[dict[Any, Employee], list[Any], dict[str, UUID]]:
    logger.info(
        "Create existing employee reference maps, checking lines: %i",
        len(employee_and_wage_info_list),
    )

    incoming_ssns = set()
    for employee_and_wage_info in employee_and_wage_info_list:
        incoming_ssns.add(employee_and_wage_info["employee_ssn"])

    ssns_to_check_in_db = list(
        filter(lambda ssn: ssn not in employee_ssns_created_in_current_import_run, incoming_ssns)
    )

    ssn_to_existing_employee_ids = dor_persistence_util.get_employee_ids_by_ssn(
        db_session, ssns_to_check_in_db
    )

    existing_employee_models = dor_persistence_util.get_employees_by_ssn(
        db_session, ssns_to_check_in_db
    )

    ssn_to_existing_employee_model = {}
    for employee in existing_employee_models:
        if employee.tax_identifier is not None:
            ssn_to_existing_employee_model[employee.tax_identifier.tax_identifier] = employee

    logger.info(
        "Done - Create existing employee reference maps - checked ssns: %i, existing employees matched: %i",
        len(ssns_to_check_in_db),
        len(existing_employee_models),
    )

    return ssn_to_existing_employee_model, ssns_to_check_in_db, ssn_to_existing_employee_ids


def process_dor_files(
    dor_files: ImportFiles,
    db_session_raw: Session,
    s3: Optional[boto3.Session] = None,
) -> List[ImportReport]:
    import_reports: List[ImportReport] = []
    employee_report_name_changes: EmployeeReportNameChanges = EmployeeReportNameChanges()

    # Initialize the decrypter
    dor_files.decrypter = dor_files.decrypter or decrypter_factory(dor_files.decrypt_files)

    with db.session_scope(db_session_raw) as db_session:
        # process each batch
        if dor_files.file_type.lower() == "initial":
            for import_batch in dor_files.import_batches:
                log_attributes = {
                    "date": import_batch.upload_date,
                    "employer_file": import_batch.employer_file,
                    "employee_file": import_batch.employee_file,
                }

                logger.info("Processing import batch", extra=log_attributes)
                import_report = importer_util.process_daily_import(
                    db_session,
                    str(import_batch.employer_file),
                    str(import_batch.employee_file),
                    employee_report_name_changes.dor_employee_data_integrity_changes,
                    dor_files.decrypter,
                )
                import_reports.append(import_report)
            s3_dest = generate_and_upload_dor_employee_integrity_file(
                employee_report_name_changes.dor_employee_data_integrity_changes
            )
            if len(employee_report_name_changes.dor_employee_data_integrity_changes) > 0:
                send_employee_integrity_report_email(s3_dest)
            else:
                send_employee_integrity_report_email()

        if dor_files.file_type.lower() == "pending_filing":
            for employer_file in dor_files.import_files:
                logger.info("Processing import file", extra={"employer_file": employer_file})
                import_report = importer_util.process_pending_filing_employer_import(
                    db_session,
                    employer_file,
                    dor_files.exemption_file_path,
                    dor_files.decrypter,
                    s3 or boto3.client("s3"),
                )
                import_reports.append(import_report)
        if dor_files.file_type.lower() == "exempt":
            for employer_file in dor_files.import_files:
                logger.info("Processing import file", extra={"employer_file": employer_file})
                import_report = importer_util.process_exempt_employer_import(
                    db_session,
                    employer_file,
                    dor_files.decrypter,
                    s3 or boto3.client("s3"),
                    dor_files.importer,
                    repush=dor_files.repush,
                )
                import_reports.append(import_report)

    return import_reports


def generate_and_upload_dor_employee_integrity_file(
    dor_employee_integrity_list: List[Dict],
) -> str:
    logger.info("Generating DOR Employee Integrity report")

    now = get_now_us_eastern()
    s3_config = dor_config.get_import_s3_config()
    dor_employee_integrity_folder_path_prefix = s3_config.DOR_EMPLOYEE_INTEGRITY_FILE_PATH_PREFIX
    timestamp_prefix = now.strftime("%Y-%m-%d-%H-%M-%S")
    dor_employee_integrity_date_stamped_file_name = (
        f"{timestamp_prefix}-{Constants.EMPLOYEE_INTEGRITY_FILE_NAME}"
    )

    dor_employee_integrity_date_stamped_file_name_with_extension = (
        f"{dor_employee_integrity_date_stamped_file_name}.csv"
    )

    tempfile_path = file_util.create_csv_from_list(
        dor_employee_integrity_list,
        Constants.EMPLOYEE_INTEGRITY_FILE_FIELDS,
        dor_employee_integrity_date_stamped_file_name,
    )

    s3_dest = dor_config.build_archive_path(
        dor_employee_integrity_folder_path_prefix,
        Constants.EMPLOYEE_INTEGRITY_FOLDER_PATH,
        dor_employee_integrity_date_stamped_file_name_with_extension,
    )

    file_util.upload_file(str(tempfile_path), s3_dest)

    logger.info("Done Generating DOR Employee Integrity report")
    return s3_dest


def send_employee_integrity_report_email(file_path: Optional[str] = "") -> None:
    logger.info("Start sending email")
    email_config = dor_config.get_email_config()

    sender = email_config.pfml_email_address
    bounce_forwarding_email_address_arn = email_config.bounce_forwarding_email_address_arn
    recipient = email_config.dor_employee_integrity_email_address

    subject = f"DOR Employee Integrity Report as of {get_now_us_eastern():%m/%d/%Y}"

    if file_path:
        # Updating S3 file location to a clickable link in the email.
        prefix = "https://s3.console.aws.amazon.com/s3/buckets/"
        file_path = file_path[5:]
        body_text_msg = f"""<html><body>Click on the link to view the employee integrity report
                        <br/><a href={prefix}{file_path}>Employee Integrity Report</a></body></html>"""
        logger.info("Email with link to file location")
    else:
        body_text_msg = "<html><body>No file generated today</body></html>"

    email_recipient = EmailRecipient(to_addresses=[recipient])

    send_email(
        recipient=email_recipient,
        subject=subject,
        body_text=body_text_msg,
        sender=sender,
        bounce_forwarding_email_address_arn=bounce_forwarding_email_address_arn,
        mime_type="html",
    )
    logger.info("Finished sending email")
