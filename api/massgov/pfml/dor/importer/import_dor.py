#!/usr/bin/python3
#
# ECS task to import DOR data from S3 to PostgreSQL (RDS).
#

import sys
import uuid
from datetime import date
from typing import Any, Dict, List

import boto3
from sqlalchemy.orm.session import Session

import massgov.pfml.db
import massgov.pfml.dor.importer.lib.dor_persistence_util as dor_persistence_util
import massgov.pfml.util.files as file_util
import massgov.pfml.util.logging as logging
import massgov.pfml.util.newrelic.events
from massgov.pfml.db.bulk import bulk_save
from massgov.pfml.db.lookup_data.employees import WagesAndContributionsDatasource
from massgov.pfml.db.models.import_log import ImportLog
from massgov.pfml.dor.importer.dor_file_formats import (
    EMPLOYEE_FORMAT,
    EMPLOYER_QUARTER_INFO_FORMAT,
    ParsedEmployerQuarterLine,
)
from massgov.pfml.dor.importer.dor_handler_utils import run_import
from massgov.pfml.dor.importer.dor_shared_utils import (
    EMPLOYEE_LINE_LIMIT,
    ImportException,
    ImportFiles,
    ImportReport,
    handle_import_exception,
    is_valid_employee_tax_id,
    process_dor_files,
)
from massgov.pfml.dor.importer.employers.employer_importer import import_employers
from massgov.pfml.dor.importer.import_wages import WageImporter
from massgov.pfml.dor.importer.paths import ImportBatch
from massgov.pfml.dor.util.process_dor_import import DORImport, ProcessDORImport
from massgov.pfml.util.bg import background_task
from massgov.pfml.util.encryption import Crypt, decrypter_factory

logger = logging.get_logger("massgov.pfml.dor.importer.import_dor")


@background_task("dor-import")
def handler() -> None:
    """ECS task main method."""
    try:
        run_import(ImportFiles(file_type="initial"), massgov.pfml.db.init(sync_lookups=True))
    except Exception:
        sys.exit(1)


def process_import_batches(
    import_batches: List[ImportBatch],
    decrypt_files: bool,
    db_session: Session,
) -> List[ImportReport]:
    try:
        decrypter = decrypter_factory(decrypt_files)
        dor_files = ImportFiles(
            import_batches=import_batches,
            decrypt_files=decrypt_files,
            decrypter=decrypter,
            file_type="initial",
        )
        import_reports: List[ImportReport] = process_dor_files(dor_files, db_session)
    except ImportException as ie:
        raise ie
    except Exception as e:
        raise ImportException("Unexpected error importing batches", type(e).__name__)

    return import_reports


class EmployeeWriter(object):
    def __init__(
        self,
        line_buffer_length: int,
        db_session: Session,
        report: ImportReport,
        employee_report_name_changes: List[Dict[str, Any]],
        report_log_entry: ImportLog,
    ):
        self.line_count: int = 0
        self.line_buffer_length: int = line_buffer_length
        self.remainder: str = ""
        self.remainder_encoded: bytes = b""
        self.lines: List = []
        self.parsed_employees_info_count: int = 0
        self.parsed_employer_quarterly_info_count: int = 0
        self.parsed_employer_quarter_exception_count: int = 0
        self.db_session: Session = db_session
        self.report: ImportReport = report
        self.employee_report_name_changes: List[Dict[str, Any]] = employee_report_name_changes
        self.report_log_entry: ImportLog = report_log_entry
        self.employee_ssns_created_in_current_import_run: Dict[str, uuid.UUID] = {}
        self.employer_fein_map: dict[str, str] = {}
        self.employees_missing_fein_mapping: list[dict] = []
        logger.info("Created EmployeeWriter, buffer length: %i", line_buffer_length)

    def flush_buffer(self) -> None:
        logger.info("Flushing buffer, %i lines", len(self.lines))

        employees_info = []
        employers_quarterly_info = []

        count = 0
        for row in self.lines:
            count += 1
            if row.startswith("A"):
                try:
                    employer_quarterly_info = EMPLOYER_QUARTER_INFO_FORMAT.parse_line(row)
                    self._set_employer_fein_map_values(employer_quarterly_info)
                    employers_quarterly_info.append(employer_quarterly_info)

                    self.parsed_employer_quarterly_info_count += 1
                except Exception:
                    logger.exception(
                        "Parse error with employer quarterly line. . Line: %i",
                        (self.line_count - len(self.lines)) + count,
                    )
                    self.report.parsed_employer_quarter_exception_count += 1

            if row.startswith("B"):
                try:
                    employee_info = EMPLOYEE_FORMAT.parse_line(row)
                    if not is_valid_employee_tax_id(employee_info, self.report):
                        logger.warning(
                            "Invalid employee tax id. Line: %i",
                            (self.line_count - len(self.lines)) + count,
                        )
                        continue
                    account_key = employee_info["account_key"]
                    filing_period = employee_info["filing_period"]
                    employer_fein = self._get_employer_fein(account_key, filing_period)

                    if employer_fein is None:
                        self.employees_missing_fein_mapping.append(
                            {
                                "employee_info": employee_info,
                                "line": (self.line_count - len(self.lines)) + count,
                            }
                        )
                        logger.warning(
                            "Missing employer FEIN mapping for employee line. Line: %i",
                            (self.line_count - len(self.lines)) + count,
                        )
                        continue

                    employee_info["employer_fein"] = employer_fein
                    employees_info.append(employee_info)
                    self.parsed_employees_info_count = self.parsed_employees_info_count + 1
                except Exception:
                    logger.exception(
                        "Parse error with employee line. Line: %i",
                        (self.line_count - len(self.lines)) + count,
                    )
                    self.report.parsed_employees_exception_count += 1

        if len(employers_quarterly_info) > 0:
            import_employer_pfml_contributions(
                self.db_session,
                employers_quarterly_info,
                self.report,
                self.report_log_entry.import_log_id,
            )

        if len(employees_info) > 0:
            wage_import = WageImporter(
                self.db_session,
                employees_info,
                self.employee_ssns_created_in_current_import_run,
                self.report,
                self.employee_report_name_changes,
                self.report_log_entry.import_log_id,
                WagesAndContributionsDatasource.DFML_REPORTED_WAGES,
            )
            wage_import.import_employees_and_wage_data(
                record_new_employees=True, should_update_wages=True
            )

        logger.info(
            "** Employer Quarterly Import Progress - created: %i, updated: %i, unmodified: %i, total: %i",
            self.report.created_employer_quarters_count,
            self.report.updated_employer_quarters_count,
            self.report.unmodified_employer_quarters_count,
            self.report.created_employer_quarters_count
            + self.report.updated_employer_quarters_count
            + self.report.unmodified_employer_quarters_count,
        )
        logger.info(
            "** Employee Import Progress - created: %i, updated: %i, unmodified: %i, total: %i, new employer log: %i",
            self.report.created_employees_count,
            self.report.updated_employees_count,
            self.report.unmodified_employees_count,
            self.report.unmodified_employees_count
            + self.report.created_employees_count
            + self.report.updated_employees_count,
            self.report.logged_employees_for_new_employer,
        )

        logger.info(
            "** Wage Import Progress: - created: %i, updated: %i, unmodified: %i, total: %i",
            self.report.created_wages_and_contributions_count,
            self.report.updated_wages_and_contributions_count,
            self.report.unmodified_wages_and_contributions_count,
            self.report.created_wages_and_contributions_count
            + self.report.updated_wages_and_contributions_count
            + self.report.unmodified_wages_and_contributions_count,
        )

        self.lines = []

    def process_employees_missing_fein_mapping(self) -> None:
        employees_info: list[dict] = []

        if len(self.employees_missing_fein_mapping) > 0:
            logger.info(
                "Attempting to process Employees initially missing account key to FEIN mapping"
            )

        for employee in self.employees_missing_fein_mapping:
            employee_info = employee["employee_info"]
            account_key = employee_info["account_key"]
            filing_period = employee_info["filing_period"]
            employer_fein = self._get_employer_fein(account_key, filing_period)

            if employer_fein is None:
                logger.exception(
                    "Unable to reconcile employer FEIN mapping for employee line. Line: %i",
                    employee["line"],
                )
                self.report.parsed_employees_exception_count += 1
                continue

            employee_info["employer_fein"] = employer_fein
            employees_info.append(employee_info)

        if len(employees_info) > 0:
            wage_import = WageImporter(
                self.db_session,
                employees_info,
                self.employee_ssns_created_in_current_import_run,
                self.report,
                self.employee_report_name_changes,
                self.report_log_entry.import_log_id,
                WagesAndContributionsDatasource.DFML_REPORTED_WAGES,
            )
            wage_import.import_employees_and_wage_data(
                record_new_employees=True, should_update_wages=True
            )

    def __call__(self, data: bytes) -> bool:
        if data:
            try:
                to_decode = self.remainder_encoded + data
                lines = str(to_decode.decode("utf-8")).split("\n")
                self.remainder_encoded = b""

                i = 0
                while i < len(lines):
                    if i != (len(lines) - 1):
                        self.lines.append(self.remainder + lines[i])
                        self.line_count = self.line_count + 1
                        self.remainder = ""
                    if i == (len(lines) - 1):
                        self.remainder = lines[i]

                    i += 1

            except UnicodeDecodeError:
                self.remainder_encoded = data

            if len(self.lines) > self.line_buffer_length:
                self.flush_buffer()

        else:
            logger.info(
                "Done parsing employee and wage rows", extra={"lines_parsed": self.line_count}
            )
            self.flush_buffer()

        return False

    def _get_employer_fein(self, account_key: str, filing_period: date) -> str | None:
        """
        Attempts to get an FEIN using the following strategies in order of preference:

        1. The last FEIN associated with an `account_key` and `filing_period` pair that appeared on
        an earlier "A" record.
        2. The last FEIN associated with an `account_key` alone that appeared on an earlier "A"
        record.
        3. The FEIN of an existing `Employer` associated with the `account_key` that was most
        recently updated by an import job.

        Returns `None` if all strategies fail to find a related FEIN.
        """

        composite_key = _make_composite_map_key(account_key, filing_period)
        employer_fein = self.employer_fein_map.get(composite_key)

        if employer_fein is not None:
            return employer_fein

        employer_fein = self.employer_fein_map.get(account_key)

        if employer_fein is not None:
            return employer_fein

        return dor_persistence_util.get_latest_employer_fein_by_account_key(
            self.db_session, account_key
        )

    def _set_employer_fein_map_values(self, employer_quarterly_info: dict) -> None:
        account_key: str = employer_quarterly_info["account_key"]
        filing_period: date = employer_quarterly_info["filing_period"]
        composite_key = _make_composite_map_key(account_key, filing_period)
        employer_fein: str = employer_quarterly_info["employer_fein"]
        self.employer_fein_map[composite_key] = employer_fein
        self.employer_fein_map[account_key] = employer_fein


def _make_composite_map_key(account_key: str, filing_period: date) -> str:
    return f"{account_key}:{filing_period}"


def process_daily_import(
    db_session: Session,
    employer_file_path: str,
    employee_file_path: str,
    employee_report_name_changes: List[Dict[str, Any]],
    decrypter: Crypt,
) -> ImportReport:
    logger.info("Starting to process files")

    dor_import = DORImport(
        employer_file_path=employer_file_path,
        employee_file_path=employee_file_path,
        decrypter=decrypter,
        report_log_source="DOR",
        report_log_import_type="Employer" if employer_file_path else "Employee",
    )
    process_dor_import = ProcessDORImport(dor_import)
    dor_import.report_log_entry = process_dor_import.create_report_log(db_session)
    try:
        # If an employer file is given, parse and import
        if employer_file_path:
            employers = process_dor_import.parse_employer_file()

            dor_import.parsed_employers_count = len(employers)
            import_employers(
                db_session, employers, dor_import.report, dor_import.report_log_entry.import_log_id
            )

        if employee_file_path:
            writer = EmployeeWriter(
                line_buffer_length=EMPLOYEE_LINE_LIMIT,
                db_session=db_session,
                report=dor_import.report,
                employee_report_name_changes=employee_report_name_changes,
                report_log_entry=dor_import.report_log_entry,
            )
            decrypter.set_on_data(writer)
            decrypter.set_on_end(writer.process_employees_missing_fein_mapping)
            file_stream = file_util.open_stream(employee_file_path, "rb")
            decrypter.decrypt_stream(file_stream)
            dor_import.parsed_employees_count = writer.parsed_employees_info_count

        # finalize report
        process_dor_import.finalize_report_log(db_session)

        logger.info(
            "Invalid employer addresses: %s",
            repr(dor_import.report.invalid_employer_addresses_by_account_key),
        )
        logger.info(
            "Sample Employer line lengths: %s",
            repr(dor_import.report.sample_employers_line_lengths),
        )

        # move file to processed folder unless explicitly told not to.
        s3 = boto3.client("s3")
        process_dor_import.move_employer_file(s3)
        process_dor_import.move_employee_file(s3)

    except Exception as e:
        handle_import_exception(e, db_session, dor_import.report_log_entry, dor_import.report)

    return dor_import.report


def import_employer_pfml_contributions(
    db_session: massgov.pfml.db.Session,
    employer_quarterly_info_list: List[ParsedEmployerQuarterLine],
    report: ImportReport,
    import_log_entry_id: int,
) -> None:
    employer_feins = {
        employee_info["employer_fein"] for employee_info in employer_quarterly_info_list
    }
    employer_fein_to_employer_id_map = dor_persistence_util.get_employers_by_employer_fein(
        db_session, employer_feins
    )

    existing_quarterly_map = dor_persistence_util.get_employer_quarterly_info_by_employer_id(
        db_session, employer_fein_to_employer_id_map.values()
    )

    # Determine which are new (employer, quarter) combinations, and which are updates to existing
    # rows in the database.
    new_employer_quarterly_contributions = {}
    found_employer_quarterly_contribution_list = []

    for employer_quarterly_info in employer_quarterly_info_list:
        employer_fein = employer_quarterly_info["employer_fein"]
        employer_id = employer_fein_to_employer_id_map.get(employer_fein, None)

        if employer_id is None:
            logger.warning(
                "Attempted to create a quarterly row for unknown employer: %s",
                employer_quarterly_info["account_key"],
            )
            report.skipped_employer_quarterly_contribution += 1
            continue

        period = employer_quarterly_info["filing_period"]
        composite_key = (employer_id, period)

        if composite_key in existing_quarterly_map:
            found_employer_quarterly_contribution_list.append(employer_quarterly_info)
        else:
            if composite_key in new_employer_quarterly_contributions:
                logger.warning(
                    "duplicate employer quarterly contribution row",
                    extra=dict(
                        account_key=employer_quarterly_info["account_key"], filing_period=period
                    ),
                )
            new_employer_quarterly_contributions[composite_key] = employer_quarterly_info

    employer_quarter_models_to_create = list(
        map(
            lambda employer_info: dor_persistence_util.dict_to_employer_quarter_contribution(
                employer_info,
                employer_fein_to_employer_id_map[employer_info["employer_fein"]],
                import_log_entry_id,
            ),
            new_employer_quarterly_contributions.values(),
        )
    )

    logger.info(
        "Creating new employer quarterly contributions records: %i",
        len(employer_quarter_models_to_create),
    )

    bulk_save(db_session, employer_quarter_models_to_create, "Employer Quarterly Contributions")

    logger.info(
        "Done - Creating new employer quarterly contributions: %i",
        len(employer_quarter_models_to_create),
    )

    report.created_employer_quarters_count += len(employer_quarter_models_to_create)

    count = 0
    updated_count = 0
    unmodified_count = 0

    for employer_info in found_employer_quarterly_contribution_list:
        count += 1
        if count % 10000 == 0:
            logger.info("Updating quarterly contribution info, current %i", count)

        employer_fein = employer_info["employer_fein"]
        employer_id = employer_fein_to_employer_id_map[employer_fein]
        filing_period = employer_info["filing_period"]

        employer_quarterly_contribution = existing_quarterly_map[(employer_id, filing_period)]

        if employer_quarterly_contribution.latest_import_log_id == import_log_entry_id:
            # Since the import log id is equal, we already updated this row during this import.
            logger.warning(
                "duplicate employer quarterly contribution row",
                extra=dict(account_key=employer_info["account_key"], filing_period=filing_period),
            )

        is_updated = dor_persistence_util.check_and_update_employer_quarterly_contribution(
            employer_quarterly_contribution, employer_info, import_log_entry_id
        )

        if is_updated:
            updated_count += 1
            report.updated_employer_quarters_count += 1
        else:
            unmodified_count += 1
            report.unmodified_employer_quarters_count += 1

    if updated_count > 0:
        logger.info(
            "Batch committing quarterly contribution updates: %i, unmodified: %i",
            updated_count,
            unmodified_count,
        )
        db_session.commit()


if __name__ == "__main__":
    handler()
