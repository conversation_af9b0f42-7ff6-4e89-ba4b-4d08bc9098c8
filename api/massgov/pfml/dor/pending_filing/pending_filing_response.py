#!/usr/bin/python3
#
# ECS task to import DOR data from S3 to PostgreSQL (RDS).
# This script is used in the employer surge process. Example runbook: https://lwd.atlassian.net/wiki/spaces/API/pages/2643886081/2023-01-03+Employer+Surge+Processing
# Due to the complexity of this script, it may be helpful to reference the Employer surge techspec: https://lwd.atlassian.net/wiki/spaces/API/pages/2154201190/Employer+Surge+Handling+Tech+Spec
#
import argparse
import os
import sys
from dataclasses import asdict, dataclass
from datetime import datetime
from typing import List

import massgov.pfml.util.logging as logging
from massgov.pfml import db
from massgov.pfml.dor import importer as importer_util
from massgov.pfml.dor.importer.dor_handler_utils import ImportRunReport, run_import
from massgov.pfml.dor.importer.dor_shared_utils import ImportException, ImportFiles
from massgov.pfml.util.bg import background_task

logger = logging.get_logger("massgov.pfml.dor.importer.pending_filing_response")

# TODO get these from environment variables
DFML_RECEIVED_FOLDER = "dfml/received/"


@dataclass
class Configuration:
    file_path: str
    exemption_file_path: str

    def __init__(self, input_args: List[str]):
        parser = argparse.ArgumentParser(description="Process DOR Pending Filing Response file")

        parser.add_argument("--responsefile", help="Path to DORDUADFML file to process.")

        parser.add_argument("--exemptionfile", help="Path to exemption file to process.")

        args = parser.parse_args(input_args)
        self.file_path = args.responsefile
        self.exemption_file_path = args.exemptionfile

        if args.responsefile is None or args.exemptionfile is None:
            raise Exception("Response file and exemption files are required.")


@background_task("dor-pending-filing-response-file")
def main():
    config = Configuration(sys.argv[1:])
    logger.info("Starting dor-pending-filing-import-file with config", extra=asdict(config))

    report = ImportRunReport(start=datetime.now().isoformat())

    with db.session_scope(db.init(), close=True) as db_session:
        try:
            file_list: list[str] = list()
            file_list.append(config.file_path)

            decrypt_files = os.getenv("DECRYPT") == "true"
            import_reports = importer_util.process_pending_filing_employer_files(
                file_list, decrypt_files, config.exemption_file_path, db_session
            )
            report.imports = import_reports
            report.message = "files imported"

        except ImportException as ie:
            report.end = datetime.now().isoformat()
            message = str(ie)
            report.message = message
            logger.error(message, extra={"report": asdict(report)})
            sys.exit(1)
        except Exception as e:
            report.end = datetime.now().isoformat()
            message = f"Unexpected error during import run: {type(e)} -- {e}"
            report.message = message
            logger.error(message, extra={"report": asdict(report)})
            sys.exit(1)

        report.end = datetime.now().isoformat()
        logger.info("Finished import run", extra={"report": asdict(report)})


@background_task("dor-pending-filing-import")
def handler() -> None:
    """ECS task main method."""
    try:
        run_import(ImportFiles(file_type="pending_filing"), db.init(sync_lookups=True))
    except Exception:
        sys.exit(1)


if __name__ == "__main__":
    handler()
