from typing import List, Optional

import boto3
from sqlalchemy.orm.session import Session

import massgov.pfml.util.logging as logging
from massgov.pfml.db.lookup_data.employees import WagesAndContributionsDatasource
from massgov.pfml.dor.importer.dor_shared_utils import (
    ImportException,
    ImportFiles,
    ImportReport,
    handle_import_exception,
    process_dor_files,
)
from massgov.pfml.dor.importer.employers.employer_importer import import_pending_filing_employers
from massgov.pfml.dor.importer.import_wages import WageImporter
from massgov.pfml.dor.util.process_dor_import import DORImport, ProcessDORImport
from massgov.pfml.util.csv import CSVSourceWrapper
from massgov.pfml.util.encryption import Crypt

logger = logging.get_logger("massgov.pfml.dor.importer.pending_filing_response")


def process_pending_filing_employer_files(
    import_files: List[str],
    decrypt_files: bool,
    exemption_file_path: str,
    db_session: Session,
    optional_decrypter: Optional[Crypt] = None,
    optional_s3: Optional[boto3.Session] = None,
) -> List[ImportReport]:
    try:
        dor_files = ImportFiles(
            import_files=import_files,
            decrypt_files=decrypt_files,
            exemption_file_path=exemption_file_path,
            decrypter=optional_decrypter,
            file_type="pending_filing",
        )
        import_reports: List[ImportReport] = process_dor_files(dor_files, db_session, optional_s3)
    except ImportException as ie:
        raise ie
    except Exception as e:
        raise ImportException("Unexpected error importing batches", type(e).__name__)

    return import_reports


def process_pending_filing_employer_import(
    db_session: Session,
    employer_file_path: str,
    employer_exemption_file_path: str,
    decrypter: Crypt,
    s3: boto3.Session,
) -> ImportReport:
    logger.info("Starting to process files")

    dor_import = DORImport(
        employer_file_path=employer_file_path,
        decrypter=decrypter,
        report_log_source="DOR",
        report_log_import_type="Pending Filing Response",
        employer_exemption_file_path=employer_exemption_file_path,
    )
    process_dor_import = ProcessDORImport(dor_import)
    exemption_data = CSVSourceWrapper(employer_exemption_file_path)
    dor_import.report_log_entry = process_dor_import.create_report_log(db_session)

    try:
        # If an employer file is given, parse and import
        if employer_file_path:
            employers, employees = process_dor_import.parse_pending_filing_employer_file(
                db_session, exemption_data
            )
            dor_import.parsed_employers_count = len(employers)
            dor_import.parsed_employees_count = len(employees)

            import_pending_filing_employers(
                db_session,
                employers,
                exemption_data,
                dor_import.report,
                dor_import.report_log_entry.import_log_id,
            )
            wage_import = WageImporter(
                db_session,
                employees,
                dict(),
                dor_import.report,
                list(),
                dor_import.report_log_entry.import_log_id,
                WagesAndContributionsDatasource.PENDING_FILING,
            )
            wage_import.import_employees_and_wage_data(should_update_wages=False)

        # finalize report
        process_dor_import.finalize_report_log(db_session)
        logger.info(
            "Sample Employer line lengths: %s",
            repr(dor_import.report.sample_employers_line_lengths),
        )

        # move file to processed folder unless explicitly told not to.
        process_dor_import.move_employer_file(s3)
        process_dor_import.move_employer_exemption_file(s3)

    except Exception as e:
        handle_import_exception(e, db_session, dor_import.report_log_entry, dor_import.report)

    return dor_import.report
