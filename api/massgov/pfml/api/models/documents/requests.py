from datetime import date
from enum import Enum
from typing import Optional

from pydantic import root_validator

from massgov.pfml.api.models.documents.common import DocumentType
from massgov.pfml.util.pydantic import PydanticBaseModel


class DocumentRequestBody(PydanticBaseModel):
    document_type: DocumentType
    name: str | None = None
    description: str | None = None
    pfml_document_type: DocumentType | None = None


class ConfirmDocumentsRequestBody(PydanticBaseModel):
    fineos_document_ids: Optional[list[str]] = []


class CertificationDocumentData(PydanticBaseModel):
    first_name: str | None = None
    last_name: str | None = None
    middle_name: str | None = None
    other_name: str | None = None
    tax_identifier: str | None = None
    date_of_birth: date | None = None
    fineos_absence_id: str | None = None

    @root_validator(pre=True, allow_reuse=True)
    @classmethod
    def validate_fields(cls, values: dict[str, object]) -> object:

        non_fineos_absence_id_field_values = [
            value
            for field, value in values.items()
            if value and field in ["first_name", "last_name", "date_of_birth", "tax_identifier"]
        ]

        # Check validation condition
        if not values.get("fineos_absence_id") and len(non_fineos_absence_id_field_values) < 2:
            raise ValueError(
                "Either fineos_absence_id must be provided or at least two values for fields first_name, last_name, date_of_birth, and tax_identifier must be non-empty in CertificationDocumentData"
            )

        return values


class CertificationDocumentIDPType(str, Enum):
    DFML_FAMILY_SERIOUS_HEALTH_COND = (
        "DFML_FAMILY_SERIOUS_HEALTH_COND"  # DFML Family Medical Condition Cert
    )
    DFML_YOUR_SERIOUS_HEALTH_COND = (
        "DFML_YOUR_SERIOUS_HEALTH_COND"  # DFML Own Medical Condition Cert
    )
    HPF_DIGITAL_CSHC = "HPF_DIGITAL_CSHC"  # Healthcare Provider Form
    WH_380_F = "WH_380_F"  # FMLA for Caring Leaving
    WH_380_E = "WH_380_E"  # FMLA for Medical leave


class CertificationDocumentRequestBody(PydanticBaseModel):
    document_type: CertificationDocumentIDPType
    document_data: CertificationDocumentData
    s3_bucket_url: str
