from enum import Enum
from typing import Any, Dict, Optional, Set, Type, Union, cast
from uuid import UUID

import flask
from connexion.lifecycle import ConnexionResponse
from werkzeug.exceptions import Forbidden, NotFound

import massgov.pfml.api.app as app
import massgov.pfml.api.util.response as response_util
import massgov.pfml.util.logging
from massgov.pfml import db
from massgov.pfml.api.authorization.exceptions import NotAuthorizedForAccess
from massgov.pfml.api.authorization.flask import READ, can, valid_access_query
from massgov.pfml.api.exceptions import ClaimWithdrawn
from massgov.pfml.api.models.applications.common import OrganizationUnit
from massgov.pfml.api.models.claims.requests import (
    ClaimSearchRequest,
    ClaimSearchTerms,
    RequestDecision,
)
from massgov.pfml.api.models.claims.responses import ClaimForPfmlCrmResponse, ClaimResponse
from massgov.pfml.api.services.claims import get_claim_detail, get_claim_from_db
from massgov.pfml.api.util.logging.requests.search import search_request_log_info
from massgov.pfml.api.util.paginate.paginator import (
    Page,
    PaginationAPIContext,
    make_pagination_params,
)
from massgov.pfml.api.util.request import parse_request_body
from massgov.pfml.api.validation.exceptions import IssueType
from massgov.pfml.db.lookup_data.employees import LeaveRequestDecision
from massgov.pfml.db.models.employees import Claim, LkLeaveRequestDecision, UserLeaveAdministrator
from massgov.pfml.db.queries.get_claims_query import GetClaimsQuery, ToDoFilterOption
from massgov.pfml.services.claims import ClaimWithdrawnError
from massgov.pfml.util import assert_never
from massgov.pfml.util.collections.dict import filter_dict
from massgov.pfml.util.duration import duration_ms_since
from massgov.pfml.util.logging.claims import (
    get_claims_search_results_info,
    get_log_attributes_from_search_info,
)
from massgov.pfml.util.logging.employers import get_employer_log_attributes
from massgov.pfml.util.sql_utils import escape_like

logger = massgov.pfml.util.logging.get_logger(__name__)


class VerificationRequired(Forbidden):
    user_leave_admin: UserLeaveAdministrator
    status_code = Forbidden

    def __init__(self, user_leave_admin, description):
        self.user_leave_admin = user_leave_admin
        self.description = description

    def to_api_response(self):
        employer_id = self.user_leave_admin.employer_id
        has_verification_data = self.user_leave_admin.employer.has_verification_data
        has_verified_leave_admin = self.user_leave_admin.employer.has_verified_leave_admin
        return response_util.error_response(
            status_code=self.status_code,
            message=self.description,
            errors=[],
            data={
                "employer_id": employer_id,
                "has_verification_data": has_verification_data,
                "has_verified_leave_admin": has_verified_leave_admin,
            },
        ).to_api_response()


def get_current_user_leave_admin_record(fineos_absence_id: str) -> UserLeaveAdministrator:
    with app.db_session() as db_session:
        associated_employer_id: Optional[UUID] = None

        current_user = app.current_user()

        claim = get_claim_from_db(fineos_absence_id)

        if claim is not None:
            associated_employer_id = claim.employer_id

        if claim is None or associated_employer_id is None:
            logger.warning(
                "Claim does not exist for given absence ID",
                extra={"absence_case_id": fineos_absence_id},
            )
            raise Forbidden(description="Claim does not exist for given absence ID")

        user_leave_admin = (
            db_session.query(UserLeaveAdministrator)
            .filter(
                (UserLeaveAdministrator.user_id == current_user.user_id)
                & (UserLeaveAdministrator.employer_id == associated_employer_id)
            )
            .one_or_none()
        )

        log_attributes = {
            "absence_case_id": fineos_absence_id,
            "user_id": current_user.user_id,
            "associated_employer_id": associated_employer_id,
        }

        if user_leave_admin is None:
            logger.warning(
                "The leave admin is None",
                extra=log_attributes,
            )
            raise NotAuthorizedForAccess(
                description="User does not have leave administrator record for this employer",
                error_type=IssueType.unauthorized_leave_admin,
            )

        if user_leave_admin.fineos_web_id is None:
            logger.warning(
                "The leave admin has no FINEOS ID",
                extra=log_attributes,
            )
            raise VerificationRequired(
                user_leave_admin, "User has no leave administrator FINEOS ID"
            )

        if not user_leave_admin.verified:
            logger.warning(
                "The leave admin is not verified",
                extra=log_attributes,
            )
            raise VerificationRequired(user_leave_admin, "User is not Verified")

        if not can(READ, claim):
            logger.warning(
                "The leave admin cannot access claims of this organization unit",
                extra={
                    "has_verification_data": user_leave_admin.employer.has_verification_data,
                    "has_verified_leave_admin": user_leave_admin.employer.has_verified_leave_admin,
                    "claim_organization_unit": OrganizationUnit.from_orm(
                        claim.organization_unit
                    ).dict(),
                    "leave_admin_fineos_web_id": user_leave_admin.fineos_web_id,
                    "leave_admin_organization_unit_ids": ",".join(
                        [str(o.organization_unit_id) for o in user_leave_admin.organization_units]
                    ),
                    **log_attributes,
                },
            )
            raise NotAuthorizedForAccess(
                description="The leave admin cannot access claims of this organization unit",
                error_type="unauthorized_leave_admin",
            )

        return user_leave_admin


def get_claim(fineos_absence_id: str) -> flask.Response:
    user = app.current_user()
    if user.is_employer:
        error = response_util.error_response(
            Forbidden,
            "Employers are not allowed to access claimant claim info",
            errors=[],
        )
        return error.to_api_response()

    claim = get_claim_from_db(fineos_absence_id)

    if claim is None:
        logger.warning(
            "get_claim failure - Claim not in PFML database.",
            extra={"absence_case_id": fineos_absence_id},
        )
        error = response_util.error_response(NotFound, "Claim not in PFML database.", errors=[])
        return error.to_api_response()

    if not can(READ, claim):
        logger.warning(
            "get_claim failure - User does not have access to claim.",
            extra={"absence_case_id": fineos_absence_id},
        )
        error = response_util.error_response(
            Forbidden, "User does not have access to claim.", errors=[]
        )
        return error.to_api_response()

    try:
        with app.db_session() as db_session:
            detailed_claim = get_claim_detail(claim, db_session)
    except ClaimWithdrawnError:
        logger.warning(
            "get_claim failure - Claim has been withdrawn. Unable to display claim status.",
            extra={
                "absence_id": claim.fineos_absence_id,
                "absence_case_id": claim.fineos_absence_id,
            },
        )
        return ClaimWithdrawn().to_api_response()
    except Exception as ex:
        logger.warning(
            f"get_claim failure - {str(ex)}",
            extra={
                "absence_id": claim.fineos_absence_id,
                "absence_case_id": claim.fineos_absence_id,
            },
        )
        raise ex  # handled by catch-all error handler in validation/__init__.py

    return response_util.success_response(
        message="Successfully retrieved claim",
        data=detailed_claim.dict(),
        status_code=200,
    ).to_api_response()


class ClaimsLoggingHelper(Enum):
    RETREIVE_CLAIMS = "retrieve_claims"
    GET_CLAIMS = "get_claims"


def retrieve_claims(body: dict) -> flask.Response:
    claim_request = parse_request_body(ClaimSearchRequest, body)

    return _process_claims_request(
        claim_request=claim_request, method_name=ClaimsLoggingHelper.RETREIVE_CLAIMS.value
    )


def get_claims() -> flask.Response:

    # TODO (PFMLPB-22429): Remove SKI application table flag
    # enable_ski_applications_table = app.get_features_config().leave_admin.enable_ski_applications_table

    # TODO (PFMLPB-22982): Remove SKI application filter flag
    enable_ski_applications_filter = (
        app.get_features_config().leave_admin.enable_ski_applications_filter
    )

    terms = parse_request_body(
        ClaimSearchTerms,
        filter_dict(
            flask.request.args,
            {
                "employee_id",
                "employer_id",
                "is_reviewable",  # TODO (PFMLPB-22982): Remove SKI application filter flag
                "request_decision",  # TODO (PFMLPB-22982): Remove SKI application filter flag
                "to_do",
                "application_status",
                "search",
            },
        ),
    )
    pagination_params = make_pagination_params(flask.request)
    claim_request = ClaimSearchRequest(
        terms=terms, order=pagination_params.order, paging=pagination_params.paging
    )

    return _process_claims_request(
        claim_request=claim_request,
        method_name=ClaimsLoggingHelper.GET_CLAIMS.value,
        enable_ski_applications_filter=enable_ski_applications_filter,
    )


def _process_claims_request(
    claim_request: ClaimSearchRequest,
    method_name: str,
    enable_ski_applications_filter: bool = False,
) -> ConnexionResponse:
    current_user = app.current_user()

    log_attributes = {}
    log_attributes.update(get_employer_log_attributes(current_user))

    with PaginationAPIContext(Claim, request=claim_request) as pagination_context:
        with app.db_session() as db_session:
            claims_query = _create_claims_search_query(
                db_session,
                claim_request.terms,
                pagination_context,
                log_attributes,
                enable_ski_applications_filter=enable_ski_applications_filter,
            )

            page = claims_query.get_paginated_results(pagination_context)
            request_log_info = search_request_log_info(claim_request, page)

            # determine how long the initial query (searching for claims) took to run
            initial_query_duration_ms = duration_ms_since(flask.g.start_time)

            response = _get_claims_response(current_user.is_pfml_crm_user, page, pagination_context)
            results_log_info = get_claims_search_results_info(page.values)

            # determine how long the request took to run, in total (including sub-queries to populate claim data)
            final_duration_ms = duration_ms_since(flask.g.start_time)

            duration_attributes = {
                "initial_query_duration_ms": initial_query_duration_ms,
                "final_duration_ms": final_duration_ms,
            }

    # Log extra information to New Relic to help diagnose performance issues on the GET claims endpoint
    # See: PFMLPB-13804
    logger.info(
        f"{method_name} success",
        extra={
            **request_log_info,
            **results_log_info,
            **log_attributes,
            **duration_attributes,
        },
    )

    return response


def _create_claims_search_query(
    db_session: db.Session,
    search_terms: ClaimSearchTerms,
    pagination_context: PaginationAPIContext,
    log_attributes: Dict[str, Any],
    enable_ski_applications_filter: bool = False,
) -> GetClaimsQuery:
    """
    Create an instance of GetClaimsQuery based on the given search terms.
    Note that this could be a classmethod in GetClaimsQuery, but we should remove the
    log_attributes first.
    """

    claims_query = GetClaimsQuery(db_session)
    claims_query.stmt = valid_access_query(db_session, READ, Claim, claims_query.stmt)

    employer_ids = search_terms.employer_ids
    if employer_ids:
        claims_query.add_employers_filter(employer_ids)

    employee_ids = search_terms.employee_ids
    if employee_ids:
        claims_query.add_employees_filter(employee_ids)

    claims_query.add_managed_requirements_filter()

    search_string = search_terms.search
    if search_string:
        claims_query.add_search_filter(
            escape_like(search_string)
        )  # escape user input search string

    # TODO (PFMLPB-22982): Remove SKI applications legacy code (else clause)
    if enable_ski_applications_filter:
        to_do = search_terms.to_do
        if to_do:
            log_attributes.update({"filter.to_do": ",".join(to_do)})
            claims_query.add_to_do_filter(to_do)

        if search_terms.is_reviewable is not None:
            is_reviewable = search_terms.is_reviewable
        else:
            is_reviewable = True if to_do and ToDoFilterOption.REVIEW_DUE.value in to_do else False

        application_status_decisions = map_application_status_param_to_db_columns(
            search_terms.application_status
        )
        if application_status_decisions:
            if search_terms.application_status:
                log_attributes.update(
                    {
                        "filter.application_status": ",".join(
                            str(d) for d in application_status_decisions
                        )
                    }
                )
            claims_query.add_request_decision_filter(application_status_decisions)

    else:
        is_reviewable = cast(bool, search_terms.is_reviewable)  # cast to fix type check issue
        if is_reviewable is not None:
            log_attributes.update({"filter.is_reviewable": "yes" if is_reviewable else "no"})
            claims_query.add_is_reviewable_filter(is_reviewable)

        request_decisions = map_request_decision_param_to_db_columns(search_terms.request_decision)
        if request_decisions:
            if search_terms.request_decision:
                log_attributes.update(
                    {"filter.request_decision": search_terms.request_decision.value}
                )
            claims_query.add_request_decision_filter(request_decisions)

    claims_query.add_order_by(pagination_context, is_reviewable)

    return claims_query


def _get_claims_response(
    is_pfml_crm_user: bool, page: Page, pagination_context: PaginationAPIContext
) -> ConnexionResponse:
    response_model: Union[Type[ClaimForPfmlCrmResponse], Type[ClaimResponse]] = (
        ClaimForPfmlCrmResponse if is_pfml_crm_user else ClaimResponse
    )

    result_log_info = get_log_attributes_from_search_info(page.values)
    logger.info(
        "get_claims response object success",
        extra={
            **result_log_info,
        },
    )

    return response_util.paginated_success_response(
        message="Successfully retrieved claims",
        model=response_model,
        page=page,
        context=pagination_context,
        status_code=200,
    ).to_api_response()


def map_request_decision_param_to_db_columns(
    request_decision: Optional[RequestDecision],
) -> Set[LkLeaveRequestDecision]:
    """
    Map an API request decision value to the corresponding database leave request decision(s).
    """
    if request_decision is RequestDecision.APPROVED:
        return {LeaveRequestDecision.APPROVED}
    elif request_decision is RequestDecision.DENIED:
        return {LeaveRequestDecision.DENIED}
    elif request_decision is RequestDecision.WITHDRAWN:
        return {LeaveRequestDecision.WITHDRAWN}
    elif request_decision is RequestDecision.PENDING:
        return {
            LeaveRequestDecision.PENDING,
            LeaveRequestDecision.IN_REVIEW,
            LeaveRequestDecision.PROJECTED,
        }
    elif request_decision is RequestDecision.CANCELLED:
        return {
            LeaveRequestDecision.CANCELLED,
            LeaveRequestDecision.VOIDED,
        }
    elif request_decision is None:
        return set()
    else:
        assert_never(request_decision)


def map_application_status_param_to_db_columns(
    application_status: Optional[Set[RequestDecision]],
) -> Set[LkLeaveRequestDecision]:
    """
    Map one or more API request decision values to the corresponding database leave request decision(s).
    """
    if not application_status:
        return set()

    application_status_decisions = set()

    for decision in application_status:
        if decision is RequestDecision.APPROVED:
            application_status_decisions.add(LeaveRequestDecision.APPROVED)
        elif decision is RequestDecision.DENIED:
            application_status_decisions.add(LeaveRequestDecision.DENIED)
        elif decision is RequestDecision.WITHDRAWN:
            application_status_decisions.add(LeaveRequestDecision.WITHDRAWN)
        elif decision is RequestDecision.PENDING:
            application_status_decisions.update(
                {
                    LeaveRequestDecision.PENDING,
                    LeaveRequestDecision.IN_REVIEW,
                    LeaveRequestDecision.PROJECTED,
                }
            )
        elif decision is RequestDecision.CANCELLED:
            application_status_decisions.update(
                {
                    LeaveRequestDecision.CANCELLED,
                    LeaveRequestDecision.VOIDED,
                }
            )
        else:
            assert_never(decision)

    return application_status_decisions
