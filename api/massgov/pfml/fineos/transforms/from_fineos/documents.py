import mimetypes
from typing import Optional, Union

import massgov.pfml.db
import massgov.pfml.fineos.models
from massgov.pfml.api.constants.documents import EMPLOYEE_LEGAL_DOC_TYPES
from massgov.pfml.api.models.documents.common import DocumentType as DocumentTypeEnum
from massgov.pfml.api.models.documents.responses import DocumentResponse
from massgov.pfml.db.models.appeal import Appeal
from massgov.pfml.db.models.applications import Application
from massgov.pfml.db.models.employees import User
from massgov.pfml.db.models.payments import Pfml1099
from massgov.pfml.fineos.models.customer_api import Base64EncodedFileData, Document


def fineos_document_to_document_response(
    fineos_document: massgov.pfml.fineos.models.customer_api.Document,
    db_session: massgov.pfml.db.Session,
    application: Optional[Application] = None,
    appeal: Optional[Appeal] = None,
    user: Optional[User] = None,
) -> DocumentResponse:
    """Constructs an instance of DocumentResponse (PFML Pydantic model) from FINEOS Document"""
    if application:
        user_id = application.user_id
    elif user:
        user_id = user.user_id
    else:
        # means this document extracted from FINEOS without associated User or Application
        # user_id (which is a PFML-only DB identifier) is unknown
        user_id = None

    created_at = None
    if fineos_document.receivedDate:
        created_at = fineos_document.receivedDate
    content_type, _ = mimetypes.guess_type(fineos_document.originalFilename or "")
    document_type_description = fineos_document.name

    # If this is a 1099, re-format the doc name to include tax year
    if document_type_description == DocumentTypeEnum.irs_1099g_tax_form_for_claimants:
        file_name = _format_1099_document_name(fineos_document, db_session)
    else:
        file_name = fineos_document.originalFilename or ""

    is_legal_notice = document_type_description in [
        doc_type.document_type_description for doc_type in EMPLOYEE_LEGAL_DOC_TYPES
    ]

    document_response = DocumentResponse(
        user_id=user_id,
        created_at=created_at,
        document_type=document_type_description,
        content_type=content_type,
        fineos_document_id=str(fineos_document.documentId),
        name=file_name,
        description=fineos_document.description or "",
        is_legal_notice=is_legal_notice,
        pfml_document_type=None,
    )
    if appeal:
        document_response.appeal_id = appeal.appeal_id
    if application:
        document_response.application_id = application.application_id
    return document_response


def _format_1099_document_name(
    fineos_doc_data: Union[Base64EncodedFileData, Document], db_session: massgov.pfml.db.Session
) -> str:

    if fineos_doc_data.description is None:
        return "Form 1099-G"

    # Description should be string like "725abe84-fd78-4a09-82b9-f484c3d4fde2.pdf"
    doc_1099_id = fineos_doc_data.description.split(".pdf")[0]

    pfml_1099_record = (
        db_session.query(Pfml1099).filter(Pfml1099.pfml_1099_id == doc_1099_id).one_or_none()
    )

    # Should always be a record, but just in case we can't find a match
    # it shouldn't block us from returning the file.
    if pfml_1099_record is None:
        return "Form 1099-G"

    amended_str = " (Amended)" if pfml_1099_record.correction_ind is True else ""

    return f"{pfml_1099_record.tax_year} Form 1099-G{amended_str}"
