"""Add employee_push_to_fineos_queue priority

Revision ID: ecf7744fb8f1
Revises: 46424ff26f0e
Create Date: 2025-06-10 20:59:56.106494

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "ecf7744fb8f1"
down_revision = "46424ff26f0e"


def upgrade():
    op.add_column(
        "employee_push_to_fineos_queue",
        sa.Column(
            "priority",
            sa.Integer(),
            server_default=sa.text("0"),
            nullable=False,
            comment="Priority for processing from the queue",
        ),
    )
    op.create_index(
        "ix_priority_created_at",
        "employee_push_to_fineos_queue",
        ["priority", "created_at"],
        unique=False,
    )
    op.drop_table_comment(
        "employee_push_to_fineos_queue",
        existing_comment="Employee records used by Eligibility Feed Export job to produces eligibility files containing employees for transfer to FINEOS\nstatus:active",
        schema=None,
    )


def downgrade():
    op.create_table_comment(
        "employee_push_to_fineos_queue",
        "Employee records used by Eligibility Feed Export job to produces eligibility files containing employees for transfer to FINEOS\nstatus:active",
        existing_comment=None,
        schema=None,
    )
    op.drop_index("ix_priority_created_at", table_name="employee_push_to_fineos_queue")
    op.drop_column("employee_push_to_fineos_queue", "priority")
