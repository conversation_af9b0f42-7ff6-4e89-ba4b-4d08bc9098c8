"""Drop deprecated employer_exemption_application columns

Revision ID: baa15248a2b8
Revises: ecf7744fb8f1
Create Date: 2025-06-16 11:50:27.815511

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "baa15248a2b8"
down_revision = "ecf7744fb8f1"


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(
        "employer_exemption_application_tpa_phone_number_id_fkey",
        "employer_exemption_application",
        type_="foreignkey",
    )
    op.drop_column("employer_exemption_application", "surety_bond_effective_date")
    op.drop_column("employer_exemption_application", "tpa_contact_name")
    op.drop_column("employer_exemption_application", "tpa_phone_number_id")
    op.drop_column("employer_exemption_application", "does_plan_cover_short_term_disability")
    op.drop_column("employer_exemption_application", "tpa_email_address")
    op.drop_column("employer_exemption_application", "does_plan_provide_enough_childcare_leave")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "employer_exemption_application",
        sa.Column(
            "does_plan_provide_enough_childcare_leave",
            sa.BOOLEAN(),
            autoincrement=False,
            nullable=True,
            comment="Denotes if plan provides at least 12 weeks of paid leave to care for a child with a serious health condition",
        ),
    )
    op.add_column(
        "employer_exemption_application",
        sa.Column(
            "tpa_email_address",
            sa.TEXT(),
            autoincrement=False,
            nullable=True,
            comment="The Third-party administrator email address associated with the Employer Exemption application\npii:true",
        ),
    )
    op.add_column(
        "employer_exemption_application",
        sa.Column(
            "does_plan_cover_short_term_disability",
            sa.BOOLEAN(),
            autoincrement=False,
            nullable=True,
            comment="Denotes if medical leave plan is covered under short term disability plan",
        ),
    )
    op.add_column(
        "employer_exemption_application",
        sa.Column(
            "tpa_phone_number_id",
            sa.UUID(),
            autoincrement=False,
            nullable=True,
            comment="Internal id for the associated Third-Party administrator Phone record",
        ),
    )
    op.add_column(
        "employer_exemption_application",
        sa.Column(
            "tpa_contact_name",
            sa.TEXT(),
            autoincrement=False,
            nullable=True,
            comment="The contact name associated with the Employer Exemption application\npii:true",
        ),
    )
    op.add_column(
        "employer_exemption_application",
        sa.Column(
            "surety_bond_effective_date",
            sa.DATE(),
            autoincrement=False,
            nullable=True,
            comment="Surety bond start date",
        ),
    )
    op.create_foreign_key(
        "employer_exemption_application_tpa_phone_number_id_fkey",
        "employer_exemption_application",
        "phone",
        ["tpa_phone_number_id"],
        ["phone_id"],
    )
    # ### end Alembic commands ###
