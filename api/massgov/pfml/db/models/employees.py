# ORM and serialization models for our employee data and API layers.
#
# A model's ORM representation should always match the database so we can
# properly read and write data. If you make a change, follow the instructions
# in the API README to generate an associated table migration.
#
# Generally, a model factory should be provided in the associated factories.py file.
# This allows us to build mock data and insert them easily in the database for tests
# and seeding.
#

from dataclasses import dataclass
from datetime import date, datetime, timedelta
from decimal import Decimal
from typing import TYPE_CHECKING, List, Optional, Tuple, cast
from uuid import UUID

from dateutil.relativedelta import relativedelta
from sqlalchemy import (
    BIGINT,
    TIMESTAMP,
    Boolean,
    ColumnElement,
    Date,
    ForeignKey,
    Index,
    Integer,
    Interval,
    Numeric,
    Text,
    UniqueConstraint,
    and_,
    asc,
    desc,
    or_,
    select,
    text,
)
from sqlalchemy.dialects.postgresql import UUID as SQL_UUID
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.orm import DynamicMapped, Mapped, Query, aliased, foreign
from sqlalchemy.orm import mapped_column as Column
from sqlalchemy.orm import relationship, validates
from sqlalchemy.schema import Sequence
from sqlalchemy.sql.expression import func
from sqlalchemy.types import JSON

import massgov.pfml.util.logging
from massgov.pfml.api.util.deepgetattr import deepgetattr
from massgov.pfml.db.models.import_log import ImportLog
from massgov.pfml.db.models.reference_file.reference_file import ReferenceFile
from massgov.pfml.util.datetime import utcnow
from massgov.pfml.util.datetime.quarter import Quarter
from massgov.pfml.util.sqlalchemy import get_object_session
from massgov.pfml.util.strings import validate_sanitized_fein

from .absences import LkAbsenceReason, LkAbsenceStatus
from .address import Address
from .base import (
    Base,
    TableStatus,
    TimestampIndexMixin,
    TimestampMixin,
    column_doc,
    deprecated_column,
    utc_timestamp_gen,
    uuid_gen,
)
from .dua import DuaEmployeeDemographics
from .fineos_web_id import FINEOSWebIdExt
from .industry_codes import LkIndustryCode
from .language import LkLanguage
from .link_claimant_address import ClaimantAddress
from .link_employee_address import EmployeeAddress
from .link_employer_address import EmployerAddress
from .link_experian_address_pair import ExperianAddressPair
from .link_health_care_provider_address import HealthCareProviderAddress
from .organization_unit import DuaReportingUnit, OrganizationUnit
from .state import LkState
from .verifications import Verification

if TYPE_CHECKING:
    from massgov.pfml.db.models.absences import AbsencePeriod
    from massgov.pfml.db.models.appeal import Appeal
    from massgov.pfml.db.models.applications import Application
    from massgov.pfml.db.models.change_request import ChangeRequest
    from massgov.pfml.db.models.documents import Document
    from massgov.pfml.db.models.payments import (
        FineosExtractVpei,
        FineosWritebackDetails,
        PaymentLine,
    )
    from massgov.pfml.db.models.reference_file.payment_reference_file import PaymentReferenceFile

logger = massgov.pfml.util.logging.get_logger(__name__)

# Prefix for Document IDs created for MMARS
PREFIX_INTF = "INTF"
# Prefix for Document IDs created for RE records sent to MMARS as an addition to `PREFIX_INTF`
PREFIX_RE = "RE"
# Prefix for Document IDs created for VCC records sent to MMARS as an addition to `PREFIX_INTF`
PREFIX_VCC = "CUST"


@dataclass
class EmployerExemption:
    employer_id: UUID
    has_family_exemption: bool
    has_medical_exemption: bool
    is_partially_exempt: bool
    is_fully_exempt: bool
    exemption_commence_date: date
    exemption_cease_date: date


class LkClaimType(Base):
    __tablename__ = "lk_claim_type"
    claim_type_id: Mapped[int] = Column(Integer, primary_key=True, autoincrement=True)
    claim_type_description: Mapped[str] = Column(Text, nullable=False)

    def __init__(self, claim_type_id, claim_type_description):
        self.claim_type_id = claim_type_id
        self.claim_type_description = claim_type_description


class LkMaritalStatus(Base):
    __tablename__ = "lk_marital_status"
    marital_status_id: Mapped[int] = Column(Integer, primary_key=True, autoincrement=True)
    marital_status_description: Mapped[str] = Column(Text, nullable=False)

    def __init__(self, marital_status_id, marital_status_description):
        self.marital_status_id = marital_status_id
        self.marital_status_description = marital_status_description


class LkGender(Base):
    # Descriptions in this table map to Fineos Enum domain #1
    __tablename__ = "lk_gender"
    gender_id: Mapped[int] = Column(Integer, primary_key=True, autoincrement=True)
    gender_description: Mapped[str] = Column(Text, nullable=False)
    fineos_gender_description = Column(Text, nullable=True)

    def __init__(self, gender_id, gender_description, fineos_gender_description):
        self.gender_id = gender_id
        self.gender_description = gender_description
        self.fineos_gender_description = fineos_gender_description

    @property
    def id(self) -> int:
        return self.gender_id


class LkOccupation(Base):
    __tablename__ = "lk_occupation"
    # TODO (PFMLPB-16002): why no autoincrement?
    occupation_id: Mapped[int] = Column(Integer, primary_key=True)
    occupation_description: Mapped[str] = Column(Text, nullable=False)

    def __init__(self, occupation_id, occupation_description):
        self.occupation_id = occupation_id
        self.occupation_description = occupation_description


class LkEducationLevel(Base):
    __tablename__ = "lk_education_level"
    education_level_id: Mapped[int] = Column(Integer, primary_key=True, autoincrement=True)
    education_level_description: Mapped[str] = Column(Text, nullable=False)

    def __init__(self, education_level_id, education_level_description):
        self.education_level_id = education_level_id
        self.education_level_description = education_level_description


class LkRole(Base):
    __tablename__ = "lk_role"
    role_id: Mapped[int] = Column(Integer, primary_key=True, autoincrement=True)
    role_description: Mapped[str] = Column(Text, nullable=False)

    def __init__(self, role_id, role_description):
        self.role_id = role_id
        self.role_description = role_description


class LkPaymentMethod(Base):
    __tablename__ = "lk_payment_method"
    payment_method_id: Mapped[int] = Column(Integer, primary_key=True, autoincrement=True)
    payment_method_description: Mapped[str] = Column(Text, nullable=False)

    def __init__(self, payment_method_id, payment_method_description):
        self.payment_method_id = payment_method_id
        self.payment_method_description = payment_method_description


class LkBankAccountType(Base):
    __tablename__ = "lk_bank_account_type"
    bank_account_type_id: Mapped[int] = Column(Integer, primary_key=True, autoincrement=True)
    bank_account_type_description: Mapped[str] = Column(Text, nullable=False)

    def __init__(self, bank_account_type_id, bank_account_type_description):
        self.bank_account_type_id = bank_account_type_id
        self.bank_account_type_description = bank_account_type_description


class LkPrenoteState(Base):
    __tablename__ = "lk_prenote_state"
    prenote_state_id: Mapped[int] = Column(Integer, primary_key=True, autoincrement=True)
    prenote_state_description: Mapped[str] = Column(Text, nullable=False)

    def __init__(self, prenote_state_id, prenote_state_description):
        self.prenote_state_id = prenote_state_id
        self.prenote_state_description = prenote_state_description


class LkPaymentRelevantParty(Base):
    __tablename__ = "lk_payment_relevant_party"
    payment_relevant_party_id: Mapped[int] = Column(Integer, primary_key=True, autoincrement=True)
    payment_relevant_party_description: Mapped[str] = Column(Text, nullable=False)

    def __init__(self, payment_relevant_party_id, payment_relevant_party_description):
        self.payment_relevant_party_id = payment_relevant_party_id
        self.payment_relevant_party_description = payment_relevant_party_description


class LkPaymentTransactionType(Base):
    __tablename__ = "lk_payment_transaction_type"
    payment_transaction_type_id: Mapped[int] = Column(Integer, primary_key=True, autoincrement=True)
    payment_transaction_type_description: Mapped[str] = Column(Text, nullable=False)

    def __init__(self, payment_transaction_type_id, payment_transaction_type_description):
        self.payment_transaction_type_id = payment_transaction_type_id
        self.payment_transaction_type_description = payment_transaction_type_description


class LkPaymentCheckStatus(Base):
    __tablename__ = "lk_payment_check_status"
    payment_check_status_id: Mapped[int] = Column(Integer, primary_key=True, autoincrement=True)
    payment_check_status_description: Mapped[str] = Column(Text, nullable=False)

    def __init__(self, payment_check_status_id, payment_check_status_description):
        self.payment_check_status_id = payment_check_status_id
        self.payment_check_status_description = payment_check_status_description


class LkTitle(Base):
    __tablename__ = "lk_title"
    title_id: Mapped[int] = Column(Integer, primary_key=True, autoincrement=True)
    title_description: Mapped[str] = Column(Text, nullable=False)

    def __init__(self, title_id, title_description):
        self.title_id = title_id
        self.title_description = title_description


class LkEligibilityDecision(Base):
    __tablename__ = "lk_eligibility_decision"
    eligibility_decision_id: Mapped[int] = Column(Integer, primary_key=True, autoincrement=True)
    eligibility_decision_description: Mapped[str] = Column(Text, nullable=False)

    def __init__(self, eligibility_decision_id, eligibility_decision_description):
        self.eligibility_decision_id = eligibility_decision_id
        self.eligibility_decision_description = eligibility_decision_description


class LkLeaveRequestDecision(Base):
    # Descriptions in this table map to Fineos Enum domain #6821
    __tablename__ = "lk_leave_request_decision"
    leave_request_decision_id: Mapped[int] = Column(Integer, primary_key=True, autoincrement=True)
    leave_request_decision_description: Mapped[str] = Column(Text, nullable=False)

    def __init__(self, leave_request_decision_id, leave_request_decision_description):
        self.leave_request_decision_id = leave_request_decision_id
        self.leave_request_decision_description = leave_request_decision_description


class LkWagesAndContributionsDatasource(Base):
    __tablename__ = "lk_wages_and_contributions_datasource"
    wages_and_contributions_datasource_id: Mapped[int] = Column(
        Integer, primary_key=True, autoincrement=True
    )
    wages_and_contributions_datasource_description: Mapped[str] = Column(Text, nullable=False)

    def __init__(
        self, wages_and_contributions_datasource_id, wages_and_contributions_datasource_description
    ):
        self.wages_and_contributions_datasource_id = wages_and_contributions_datasource_id
        self.wages_and_contributions_datasource_description = (
            wages_and_contributions_datasource_description
        )


class AuthorizedRepresentative(Base, TimestampMixin):
    """An authorized representative of a employee."""

    __tablename__ = "authorized_representative"
    __table_status__ = TableStatus.OBSOLETE

    authorized_representative_id = Column(
        SQL_UUID(as_uuid=True), primary_key=True, default=uuid_gen
    )
    first_name = Column(Text)
    last_name = Column(Text)

    employees: Mapped[list["AuthorizedRepEmployee"]] = relationship(
        "AuthorizedRepEmployee", back_populates="authorized_rep"
    )


class HealthCareProvider(Base, TimestampMixin):
    """Healthcare Provider for an employee"""

    __tablename__ = "health_care_provider"

    health_care_provider_id = Column(
        SQL_UUID(as_uuid=True),
        primary_key=True,
        default=uuid_gen,
        comment="Unique primary key id",
    )
    provider_name = Column(
        Text,
        comment="Name of healthcare provider",
    )

    addresses: Mapped[list["HealthCareProviderAddress"]] = relationship(
        "HealthCareProviderAddress", back_populates="health_care_provider"
    )


class EmployerQuarterlyContribution(Base, TimestampMixin):
    """The amount an employer contributed to PFML for the given quarter"""

    __tablename__ = "employer_quarterly_contribution"

    employer_id: Mapped[UUID] = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey("employer.employer_id"),
        index=True,
        primary_key=True,
        comment="Internal id of employer",
    )
    filing_period: Mapped[date] = Column(
        Date,
        primary_key=True,
        comment="Quarterly filing period for which employer contributed",
    )
    employer_total_pfml_contribution: Mapped[Decimal] = Column(
        Numeric(asdecimal=True),
        nullable=False,
        comment="Employer contribution for quarter",
    )
    pfm_account_id = Column(
        Text,
        nullable=False,
        index=True,
        comment="PFM account id for employer",
    )
    dor_received_date = Column(
        TIMESTAMP(timezone=True),
        comment="Date contribution was received by DOR",
    )
    dor_updated_date = Column(
        TIMESTAMP(timezone=True),
        comment="Date contribution was updated by DOR",
    )
    latest_import_log_id = Column(
        Integer,
        ForeignKey("import_log.import_log_id"),
        index=True,
        comment="id value of latest import that modified this row",
    )

    employer: Mapped["Employer"] = relationship(
        "Employer", back_populates="employer_quarterly_contributions"
    )


class StatutorilyExcludedEmployer(Base, TimestampMixin):
    """
    A statutorily excluded employer is exempt from PFML by law, such employers do not need
    to file an exemption with PFML and do not need to report wages to PFML. These employers
    may report wages to DUA though. Examples of such employers are municipalities, schools,
    and churches.

    This is not an exhaustive list of statutorily excluded employers.

    Note: This is imported directly from a cumulative CSV and does not have a staging table.
    Use the latest reference file to get the current list of known statutorily excluded employers.
    """

    __tablename__ = "statutorily_excluded_employer"

    statutorily_excluded_employer_id: Mapped[UUID] = Column(
        SQL_UUID(as_uuid=True),
        primary_key=True,
        default=uuid_gen,
        comment="Internal primary key",
    )
    employer_fein: Mapped[str] = Column(
        Text,
        nullable=False,
        index=True,
        comment=column_doc("FEIN for this company", pii=True),
    )
    description = Column(Text, comment="A description of the employer")

    reference_file_id = Column(
        SQL_UUID(as_uuid=True), ForeignKey("reference_file.reference_file_id"), index=True
    )
    import_log_id = Column(Integer, ForeignKey("import_log.import_log_id"), index=True)
    reference_file: Mapped[Optional[ReferenceFile]] = relationship(ReferenceFile)

    @hybrid_property
    def is_current(self) -> bool:
        """Did this record come from the latest statutorily excluded employer file?"""
        # inlined to avoid circular imports
        from massgov.pfml.db.lookup_data.reference_file_type import ReferenceFileType

        return self.reference_file_id == ReferenceFile.latest_reference_file_id_of_type(
            ReferenceFileType.STATUTORILY_EXCLUDED_EMPLOYER_FILE
        )


class Employer(Base, TimestampIndexMixin):
    """An employer in Massachusetts"""

    __tablename__ = "employer"

    employer_id: Mapped[UUID] = Column(
        SQL_UUID(as_uuid=True),
        primary_key=True,
        default=uuid_gen,
        comment="Internal primary key",
    )
    account_key = Column(
        Text,
        index=True,
        comment="DOR account key for this employer",
    )
    employer_fein: Mapped[str] = Column(
        Text,
        nullable=False,
        index=True,
        comment=column_doc("FEIN for this company", pii=True),
    )
    employer_name = Column(
        Text,
        comment="Official employer name",
    )
    employer_dba = Column(
        Text,
        comment="Doing Business As (DBA)",
    )
    family_exemption = Column(
        Boolean,
        comment="Employer has an exemption from Family PFML",
    )
    medical_exemption = Column(
        Boolean,
        comment="Employer has an exemption from Medical PFML",
    )
    statutory_exemption: Mapped[Optional[bool]] = deprecated_column(
        Boolean,
        comment="DEPRECATED: Use `skip_load_service_agreement_to_fineos` instead.",
        nullable=False,
        server_default="FALSE",
    )
    skip_load_service_agreement_to_fineos = Column(
        Boolean,
        comment=(
            "Employer should skip load-service-agreement-to-fineos as marked by Admin "
            "due to statutory exemption, dissolved business, fraud business, or merged business."
        ),
        nullable=False,
        server_default="FALSE",
    )

    statutory_exclusion: Mapped[Optional["StatutorilyExcludedEmployer"]] = relationship(
        "StatutorilyExcludedEmployer",
        primaryjoin=lambda: and_(
            foreign(StatutorilyExcludedEmployer.employer_fein) == Employer.employer_fein,
            StatutorilyExcludedEmployer.is_current,
        ),
        lazy="raise",  # do not load this from an instance, only via SQL
        viewonly=True,
    )

    @hybrid_property
    def is_statutorily_excluded(self) -> bool:
        """
        Some employers are excluded from participating in PFML by statute. This property
        checks whether this employer's FEIN is on the latest manually-defined list of
        statutorily excluded employers.

        (This is orthogonal to the boolean `skip_load_service_agreement_to_fineos` column,
        with which some statutorily excluded employers were previously marked, and which
        was initially named `statutory_exemption`.)
        """
        # (intentionally use != None so it works in both contexts of hybrid property)
        return self.statutory_exclusion != None  # noqa: E711

    exemption_commence_date = Column(
        Date,
        comment="Date that exemption starts",
    )
    exemption_cease_date = Column(
        Date,
        comment="Date that exemption ends",
    )
    dor_updated_date = Column(
        TIMESTAMP(timezone=True),
        comment="Updated date in DOR data feed",
    )
    latest_import_log_id = Column(
        Integer,
        ForeignKey("import_log.import_log_id"),
        index=True,
        comment="id value of latest import that modified this row",
    )
    fineos_employer_id = Column(
        Integer,
        index=True,
        unique=True,
        comment="Customer number of this employer in FINEOS",
    )
    industry_code_id = Column(
        Integer,
        ForeignKey("lk_industry_code.industry_code_id"),
        comment="Industry code relationship",
    )

    claims: Mapped[List["Claim"]] = relationship(back_populates="employer")
    wages_and_contributions: DynamicMapped["WagesAndContributions"] = relationship(
        "WagesAndContributions", back_populates="employer"
    )
    addresses: DynamicMapped["EmployerAddress"] = relationship(
        "EmployerAddress", back_populates="employer"
    )
    employer_occupations: DynamicMapped["EmployeeOccupation"] = relationship(
        "EmployeeOccupation", back_populates="employer"
    )
    employer_quarterly_contributions: Mapped[list["EmployerQuarterlyContribution"]] = relationship(
        "EmployerQuarterlyContribution", back_populates="employer"
    )
    employee_benefit_year_contributions: DynamicMapped["BenefitYearContribution"] = relationship(
        "BenefitYearContribution", back_populates="employer"
    )

    lk_industry_code: Mapped[Optional[LkIndustryCode]] = relationship(LkIndustryCode)

    mtc_number = Column(
        Text,
        unique=True,
        comment="MTC PFML ID for employer",
    )

    # Organization Units are sync'd from FINEOS from multiple different files,
    # at different times, that are fed from different data sources, so they
    # don't always include the same information.
    #
    # Notably, if an Org Unit doesn't have a `fineos_id` value, then we don't
    # really want to operationally treat it is usable yet.
    #
    # So this "all" version is for situations where we aren't _using_ the Org
    # Unit info, but might want to get the complete list of every Org Unit that
    # is attached to the Employer for export or comparison or similar.
    #
    # But generally the "fully synced" version is almost certainly what you
    # want, which is why it is what powers the `organization_unit` property.
    organization_units_all_query: DynamicMapped["OrganizationUnit"] = relationship(
        "OrganizationUnit", uselist=True, viewonly=True
    )

    organization_units_fully_synced_query: DynamicMapped["OrganizationUnit"] = relationship(
        "OrganizationUnit",
        primaryjoin="and_(OrganizationUnit.employer_id==Employer.employer_id, OrganizationUnit.fineos_id.isnot(None))",
        uselist=True,
        viewonly=True,
    )

    user_leave_administrators: Mapped[list["UserLeaveAdministrator"]] = relationship(
        "UserLeaveAdministrator", back_populates="employer", uselist=True
    )
    employer_dor_exemptions: Mapped[list["massgov.pfml.db.models.dor.EmployerDORExemption"]] = (
        relationship("EmployerDORExemption", back_populates="employer", uselist=True)
    )

    @property
    def organization_units(self) -> list[OrganizationUnit]:
        return self.organization_units_fully_synced_query.all()

    @property
    def uses_organization_units(self) -> bool:
        return self.organization_units_fully_synced_query.count() > 0

    @property
    def verification_data(self) -> Optional[EmployerQuarterlyContribution]:
        """Get the most recent withholding data. Portal uses this data in order to verify a
        user can become a leave admin for this employer"""

        current_date = date.today()
        last_years_date = current_date - relativedelta(years=1)

        # Check the last four quarters. Does not include the current quarter, which would be a future filing period.
        non_zero_contribution = (
            get_object_session(self)
            .query(EmployerQuarterlyContribution)
            .filter(EmployerQuarterlyContribution.employer_id == self.employer_id)
            .filter(EmployerQuarterlyContribution.employer_total_pfml_contribution > 0)
            .filter(
                EmployerQuarterlyContribution.filing_period.between(last_years_date, current_date)
            )
            .order_by(desc(EmployerQuarterlyContribution.filing_period))
            .first()
        )

        if non_zero_contribution is None:
            # If this is a new or previously-exempt Employer to the program, we may not
            # have any non-zero contributions within the past year. We still want to support
            # verification for them, so for them we check future filing periods, which includes
            # the current quarter:
            non_zero_contribution = (
                get_object_session(self)
                .query(EmployerQuarterlyContribution)
                .filter(EmployerQuarterlyContribution.employer_id == self.employer_id)
                .filter(EmployerQuarterlyContribution.employer_total_pfml_contribution > 0)
                .filter(EmployerQuarterlyContribution.filing_period > current_date)
                .order_by(desc(EmployerQuarterlyContribution.filing_period))
                .first()
            )

        return non_zero_contribution

    @property
    def has_verification_data(self) -> bool:
        current_date = date.today()
        last_years_date = current_date - relativedelta(years=1)

        return any(
            quarter.employer_total_pfml_contribution > 0
            and quarter.filing_period >= last_years_date
            and quarter.filing_period <= current_date
            for quarter in self.employer_quarterly_contributions
        ) or any(
            quarter.employer_total_pfml_contribution > 0 and quarter.filing_period > current_date
            for quarter in self.employer_quarterly_contributions
        )

    @property
    def has_verified_leave_admin(self) -> bool:
        return any(
            leave_admin.deactivated is False and leave_admin.verification_id is not None
            for leave_admin in self.user_leave_administrators
        )

    @property
    def is_dummy(self) -> bool:
        # check both formatted and unformatted dummy FEIN to futureproof this
        dummy_fein = {"000000000", "00-0000000"}
        return self.employer_fein in dummy_fein

    @validates("employer_fein")
    def validate_employer_fein(self, key: str, employer_fein: str) -> str:
        employer_fein = str(employer_fein)
        return validate_sanitized_fein(employer_fein)

    def get_exemption_list(
        self, effective_date: Optional[date]
    ) -> Optional[list[EmployerExemption]]:
        from massgov.pfml.db.models.dor import EmployerDORExemption

        cte = (
            get_object_session(self)
            .query(EmployerDORExemption)
            .distinct(EmployerDORExemption.employer_id, EmployerDORExemption.dor_activity_key)
            .filter(EmployerDORExemption.employer_id == self.employer_id)
            .order_by(
                EmployerDORExemption.employer_id.desc(),
                EmployerDORExemption.dor_activity_key.desc(),
                EmployerDORExemption.import_log_id.desc(),
                EmployerDORExemption.decision_commence_date.asc(),
            )
        ).cte("exemptions_cte")

        if effective_date:
            query = (
                get_object_session(self)
                .query(cte)
                .filter(
                    func.date(effective_date).between(
                        cte.c.decision_commence_date,
                        cte.c.decision_cease_date,
                    )
                )
            )

        exemptions = query.all()

        return (
            [
                EmployerExemption(
                    employer_id=self.employer_id,
                    is_fully_exempt=e.family_exemption and e.medical_exemption,
                    is_partially_exempt=(
                        (e.family_exemption and not e.medical_exemption)
                        or (not e.family_exemption and e.medical_exemption)
                    ),
                    has_family_exemption=e.family_exemption,
                    has_medical_exemption=e.medical_exemption,
                    exemption_commence_date=e.decision_commence_date,
                    exemption_cease_date=e.decision_cease_date,
                )
                for e in exemptions
            ]
            if exemptions
            else None
        )

    def get_exemption_details(self, effective_date: date) -> EmployerExemption:
        exemption = self.get_exemption_list(effective_date=effective_date)
        return (
            EmployerExemption(
                employer_id=self.employer_id,
                has_family_exemption=False,
                has_medical_exemption=False,
                is_partially_exempt=False,
                is_fully_exempt=False,
                exemption_commence_date=date(9999, 12, 31),
                exemption_cease_date=date(9999, 12, 31),
            )
            if exemption is None
            else exemption[0]
        )

    def has_family_exemption(self, effective_date: date) -> bool:
        return self.get_exemption_details(effective_date=effective_date).has_family_exemption

    def has_medical_exemption(self, effective_date: date) -> bool:
        return self.get_exemption_details(effective_date=effective_date).has_medical_exemption

    def has_partial_exemption(self, effective_date: date) -> bool:
        return self.get_exemption_details(effective_date=effective_date).is_partially_exempt

    def has_full_exemption(self, effective_date: date) -> bool:
        return self.get_exemption_details(effective_date=effective_date).is_fully_exempt

    @property
    def is_exempt_family(self) -> bool:
        return self.has_family_exemption(effective_date=date.today())

    @property
    def is_exempt_medical(self) -> bool:
        return self.has_medical_exemption(effective_date=date.today())

    @property
    def is_partially_exempt(self) -> bool:
        return self.has_partial_exemption(effective_date=date.today())

    @property
    def is_fully_exempt(self) -> bool:
        return self.has_full_exemption(effective_date=date.today())


class EmployerPushToFineosQueue(Base, TimestampMixin):
    """Employer records used by Employer Load job to create or update employers in FINEOS using a FINEOS API"""

    __tablename__ = "employer_push_to_fineos_queue"

    employer_push_to_fineos_queue_id = Column(
        SQL_UUID(as_uuid=True),
        primary_key=True,
        default=uuid_gen,
        comment="Unique primary key id",
    )
    employer_id = Column(
        SQL_UUID(as_uuid=True),
        index=True,
        comment="Internal id of employer",
    )
    action = Column(
        Text,
        index=True,
        comment="Action taking place on employer record",
    )
    modified_at = Column(
        TIMESTAMP(timezone=True),
        default=utc_timestamp_gen,
        comment="Time employer record was modified",
    )
    process_id = Column(
        Integer,
        index=True,
        comment="Process id of action on employer record",
    )
    family_exemption = Column(
        Boolean,
        comment="Whether or not the employer has a family exemption",
    )
    medical_exemption = Column(
        Boolean,
        comment="Whether or not the employer has a medical exemption",
    )
    exemption_commence_date = Column(
        Date,
        comment="Commencement date of exemption",
    )
    exemption_cease_date = Column(
        Date,
        comment="Cease date of exemption",
    )


class EFT(Base, TimestampMixin):
    """An EFT record from the legacy MMARS integration"""

    __tablename__ = "eft"
    __table_status__ = TableStatus.OBSOLETE
    eft_id = Column(SQL_UUID(as_uuid=True), primary_key=True, default=uuid_gen)
    routing_nbr = Column(Text, nullable=False)
    account_nbr = Column(Text, nullable=False)
    bank_account_type_id = Column(
        Integer, ForeignKey("lk_bank_account_type.bank_account_type_id"), nullable=False
    )
    employee_id: Mapped[Optional[UUID]] = Column(
        SQL_UUID(as_uuid=True), ForeignKey("employee.employee_id"), index=True
    )

    bank_account_type: Mapped[LkBankAccountType] = relationship(LkBankAccountType)
    employee: Mapped[Optional["Employee"]] = relationship("Employee", back_populates="efts")


class PubEft(Base, TimestampMixin):
    """Electronic Fund Transfer information used for ACH transfer payments"""

    __tablename__ = "pub_eft"
    pub_eft_id: Mapped[UUID] = Column(
        SQL_UUID(as_uuid=True), primary_key=True, default=uuid_gen, comment="Internal primary key"
    )
    routing_nbr: Mapped[str] = Column(
        Text, nullable=False, comment=column_doc("Routing number", pii=True)
    )
    account_nbr: Mapped[str] = Column(
        Text, nullable=False, comment=column_doc("Account number", pii=True)
    )
    bank_account_type_id: Mapped[int] = Column(
        Integer,
        ForeignKey("lk_bank_account_type.bank_account_type_id"),
        nullable=False,
        comment="Account type (checking, savings, etc.)",
    )
    prenote_state_id = Column(
        Integer,
        ForeignKey("lk_prenote_state.prenote_state_id"),
        nullable=False,
        comment="If the EFT information has been prenoted",
    )
    prenote_approved_at = Column(TIMESTAMP(timezone=True), comment="When prenote was approved")
    prenote_response_at = Column(
        TIMESTAMP(timezone=True),
        comment="When prenote response (indicating rejection) was received",
    )
    prenote_sent_at = Column(TIMESTAMP(timezone=True), comment="When prenote was sent")
    prenote_response_reason_code = Column(Text, comment="Reason prenote was rejected")
    pub_eft_individual_id_seq: Sequence = Sequence("pub_eft_individual_id_seq")
    pub_individual_id = Column(
        Integer,
        pub_eft_individual_id_seq,
        index=True,
        server_default=pub_eft_individual_id_seq.next_value(),
        comment="Unique ID used to track payments sent to M&T bank",
    )

    fineos_employee_first_name = Column(
        Text, comment=column_doc("First name of employee", pii=True)
    )
    fineos_employee_middle_name = Column(
        Text, comment=column_doc("Middle name of employee", pii=True)
    )
    fineos_employee_last_name = Column(Text, comment=column_doc("Last name of employee", pii=True))

    bank_account_type: Mapped[LkBankAccountType] = relationship(LkBankAccountType)
    prenote_state: Mapped[LkPrenoteState] = relationship(LkPrenoteState)

    employees: Mapped[list["EmployeePubEftPair"]] = relationship(
        "EmployeePubEftPair", back_populates="pub_eft"
    )


class TaxIdentifier(Base, TimestampMixin):
    """Tax Identifier (e.g., SSN, ITIN)"""

    __tablename__ = "tax_identifier"

    tax_identifier_id: Mapped[UUID] = Column(
        SQL_UUID(as_uuid=True),
        primary_key=True,
        default=uuid_gen,
        comment="Unique primary key id",
    )
    tax_identifier: Mapped[str] = Column(
        Text,
        nullable=False,
        unique=True,
        comment=column_doc("Tax Identifier/SSN number", pii=True),
    )

    employee: Mapped["Employee"] = relationship("Employee", back_populates="tax_identifier")

    @hybrid_property
    def tax_identifier_last4(self) -> str:
        return self.tax_identifier[-4:]

    @tax_identifier_last4.inplace.expression
    @classmethod
    def _tax_identifier_last4_expression(cls):
        return func.right(cls.tax_identifier, 4)


class Employee(Base, TimestampIndexMixin):
    """A Massachusetts employee"""

    __tablename__ = "employee"

    employee_id: Mapped[UUID] = Column(
        SQL_UUID(as_uuid=True),
        primary_key=True,
        default=uuid_gen,
        comment="Unique primary key id",
    )
    tax_identifier_id: Mapped[Optional[UUID]] = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey("tax_identifier.tax_identifier_id"),
        index=True,
        unique=True,
        comment="Internal id of tax identifier associated with employee",
    )
    title_id: Mapped[Optional[int]] = Column(
        Integer,
        ForeignKey("lk_title.title_id"),
        comment='id corresponding to a Title enum, eg 2 ("Mr")',
    )
    first_name: Mapped[str] = Column(
        Text,
        nullable=False,
        index=True,
        comment=column_doc("The employee's first name", pii=True),
    )
    middle_name: Mapped[str | None] = Column(
        Text,
        index=True,
        comment=column_doc("The employee's middle name", pii=True),
    )
    last_name: Mapped[str] = Column(
        Text,
        nullable=False,
        index=True,
        comment=column_doc("The employee's last name", pii=True),
    )
    other_name: Mapped[Optional[str]] = Column(
        Text,
        index=True,
        comment=column_doc("The employee's other name if applicable", pii=True),
    )
    email_address = Column(
        Text,
        index=True,
        comment=column_doc("The employee's email address", pii=True),
    )
    phone_number = Column(
        Text,
        index=True,
        comment=column_doc("The employee's phone number", pii=True),
    )  # Formatted in E.164
    cell_phone_number = Column(
        Text,
        index=True,
        comment=column_doc("The employee's cell phone number", pii=True),
    )  # Formatted in E.164
    preferred_comm_method_type = Column(
        Text,
        comment="The employee's preferred method of communication",
    )
    date_of_birth = Column(
        Date,
        comment=column_doc("The employee's birth date", pii=True),
    )
    date_of_death = Column(
        Date,
        comment=column_doc("The employee's death date", pii=True),
    )
    # https://lwd.atlassian.net/browse/PORTAL-439 will make this unique
    fineos_customer_number = Column(
        Text,
        nullable=True,
        index=True,
        comment="Unique fineos customer number that represents employee",
    )
    fineos_c_number = Column(
        Text,
        nullable=True,
        comment="Non unique fineos class id, used as part of a compound key for claimants",
    )
    fineos_i_number = Column(
        Text,
        nullable=True,
        comment="Maybe unique fineos id, used as part of a compound key for claimants",
    )
    marital_status_id = Column(
        Integer,
        ForeignKey("lk_marital_status.marital_status_id"),
        comment='id corresponding to a MaritalStatus enum, eg 1 ("Single")',
    )
    gender_id = Column(
        Integer,
        ForeignKey("lk_gender.gender_id"),
        comment='id corresponding to a Gneder enum, eg 1 ("Woman")',
    )
    occupation_id = Column(
        Integer,
        ForeignKey("lk_occupation.occupation_id"),
        comment='id corresponding to a Occupation enum, eg 1 ("Healthcare")',
    )
    education_level_id = Column(
        Integer,
        ForeignKey("lk_education_level.education_level_id"),
        comment='id corresponding to a EducationLevel enum, eg 1 ("Degree")',
    )
    latest_import_log_id = Column(
        Integer,
        ForeignKey("import_log.import_log_id"),
        index=True,
        comment="Value of latest import log id to create/update employee record",
    )
    payment_method_id = Column(
        Integer,
        ForeignKey("lk_payment_method.payment_method_id"),
        comment='id corresponding to a PaymentMethod enum, eg 1 ("Elec Funds Transfer")',
    )
    ctr_vendor_customer_code = Column(
        Text,
        comment="Employee's vendor customer code",
    )
    mass_id_number = Column(
        Text,
        comment=column_doc("The employee's Massachusetts state id number", pii=True),
    )
    out_of_state_id_number = Column(
        Text,
        comment=column_doc("The employee's out of state id number", pii=True),
    )

    fineos_employee_first_name = Column(
        Text,
        index=True,
        comment=column_doc("First name of employee in Fineos", pii=True),
    )
    fineos_employee_middle_name = Column(
        Text,
        index=True,
        comment=column_doc("Middle name of employee in Fineos", pii=True),
    )
    fineos_employee_last_name = Column(
        Text,
        index=True,
        comment=column_doc("Last name of employee in Fineos", pii=True),
    )

    ctr_billing_address_id = Column(
        Text,
        comment="Employee's vendor Address ID",
    )

    title: Mapped[Optional[LkTitle]] = relationship(LkTitle)
    marital_status: Mapped[Optional[LkMaritalStatus]] = relationship(LkMaritalStatus)
    gender: Mapped[Optional[LkGender]] = relationship(LkGender)
    # Note: We should move occupation to new EmployeeOccupation model
    # if this field relates to the function employee performs in a
    # specific employer. If it is a description of their profession
    # it should stay here.
    # Evaluate impact of change if it is appropriate to move.
    occupation: Mapped[Optional[LkOccupation]] = relationship(LkOccupation)
    education_level: Mapped[Optional[LkEducationLevel]] = relationship(LkEducationLevel)
    latest_import_log: Mapped[Optional["ImportLog"]] = relationship("ImportLog")
    claims: Mapped[list["Claim"]] = relationship("Claim", back_populates="employee")
    state_logs: Mapped[list["StateLog"]] = relationship("StateLog", back_populates="employee")
    efts: Mapped[list["EFT"]] = relationship("EFT", back_populates="employee")
    pub_efts: DynamicMapped["EmployeePubEftPair"] = relationship(
        "EmployeePubEftPair", back_populates="employee"
    )
    addresses: Mapped[list["Address"]] = relationship(
        "Address", secondary="link_employee_address", viewonly=True
    )
    claimant_addresses: Mapped[list["ClaimantAddress"]] = relationship("ClaimantAddress")
    payment_method: Mapped[Optional[LkPaymentMethod]] = relationship(
        LkPaymentMethod, foreign_keys=payment_method_id
    )
    tax_identifier: Mapped[Optional[TaxIdentifier]] = relationship(
        "TaxIdentifier", back_populates="employee"
    )
    authorized_reps: DynamicMapped["AuthorizedRepEmployee"] = relationship(
        "AuthorizedRepEmployee", back_populates="employee"
    )
    wages_and_contributions: DynamicMapped["WagesAndContributions"] = relationship(
        "WagesAndContributions", back_populates="employee"
    )
    employee_addresses: DynamicMapped["EmployeeAddress"] = relationship(
        "EmployeeAddress", back_populates="employee"
    )
    employee_occupations: DynamicMapped["EmployeeOccupation"] = relationship(
        "EmployeeOccupation", back_populates="employee"
    )

    benefit_years: Mapped[list["BenefitYear"]] = relationship(
        "BenefitYear", back_populates="employee"
    )
    employer_benefit_year_contributions: DynamicMapped["BenefitYearContribution"] = relationship(
        "BenefitYearContribution", back_populates="employee"
    )

    # This will be null unless FINEOS reports invalid benefit year dates for the employee,
    # then column is updated to include the date on which we first received and processed
    # the invalid dates from FINEOS.
    invalid_benefit_years_since: Mapped[Optional[datetime]] = Column(
        TIMESTAMP(timezone=True),
        index=True,
        default=None,
        comment="Date on which first received and processed the invalid dates from FINEOS",
    )

    employee_doc_id_seq: Sequence = Sequence(
        "employee_doc_id_seq", data_type=BIGINT, metadata=Base.metadata
    )

    doc_id = deprecated_column(
        Text, nullable=True, unique=True, comment="DOC_ID assigned to the VCC records sent to MMARS"
    )

    ctr_doc_id = Column(
        Text, nullable=True, unique=True, comment="DOC_ID assigned to the VCC records sent to MMARS"
    )

    # Function to get or set the unique ID
    @property
    def generate_unique_doc_id(self) -> str:

        if self.ctr_doc_id is not None:
            return self.ctr_doc_id
        else:
            # If the ctr_doc_id is not set, generate a new one
            # The format is "INTFVC" + 14-digit sequence number
            db_session = get_object_session(self)

            if not db_session:
                raise Exception("No session found")

            next_val = db_session.execute(self.employee_doc_id_seq.next_value()).scalar()

            if next_val is None or not isinstance(next_val, int):
                raise ValueError("Expected a string result, but got None or a non-string value")

            padded_val = str(next_val).zfill(12)
            self.ctr_doc_id = f"{PREFIX_INTF}{PREFIX_VCC}{padded_val}"

            return self.ctr_doc_id

    @property
    def latest_mass_id_number_from_id_proofed_applications(self) -> Optional[str]:
        # This is imported here to prevent circular import error
        from massgov.pfml.db.models.applications import Application

        application_query = (
            get_object_session(self)
            .query(Application)
            .join(Claim, Claim.claim_id == Application.claim_id)
            .join(LeaveRequest, Claim.claim_id == LeaveRequest.claim_id, isouter=True)
            .filter(Claim.employee_id == self.employee_id, LeaveRequest.is_id_proofed.is_(True))
            .order_by(desc(Claim.created_at), asc(LeaveRequest.created_at))
        )
        application = application_query.first()

        if application:
            return application.mass_id
        else:
            return None

    @property
    def latest_dob_from_applications(self) -> Optional[Date]:
        # This is imported here to prevent circular import error
        from massgov.pfml.db.models.applications import Application

        application_query = (
            get_object_session(self)
            .query(Application)
            .join(Claim, Claim.claim_id == Application.claim_id)
        )
        filters = [Claim.employee_id == self.employee_id, Claim.fineos_absence_status is not None]
        order_by = [desc(Claim.created_at)]

        application = application_query.filter(*filters).order_by(*order_by).first()  # type: ignore

        if application:
            return application.date_of_birth
        else:
            return None

    def fineos_web_id(self, employee_ssn: str, employer_fein: str) -> Optional[str]:
        fineos_web_id_ext = (
            get_object_session(self)
            .query(FINEOSWebIdExt)
            .filter(
                FINEOSWebIdExt.employee_tax_identifier == str(employee_ssn),
                FINEOSWebIdExt.employer_fein == str(employer_fein),
            )
            .one_or_none()
        )

        if fineos_web_id_ext:
            return fineos_web_id_ext.fineos_web_id

        return None

    def get_organization_units(self, employer: Employer) -> list[OrganizationUnit]:
        if not self.fineos_customer_number:
            return []

        occupation_org_units_query = (
            get_object_session(self)
            .query(OrganizationUnit)
            .join(
                EmployeeOccupation,
                and_(
                    EmployeeOccupation.employee_id == self.employee_id,
                    EmployeeOccupation.employer_id == employer.employer_id,
                    EmployeeOccupation.organization_unit_id
                    == OrganizationUnit.organization_unit_id,
                ),
            )
            .filter(
                OrganizationUnit.fineos_id.isnot(None),
            )
            .distinct()
        )

        dua_demo_org_units_query = (
            get_object_session(self)
            .query(OrganizationUnit)
            .join(
                DuaEmployeeDemographics,
                and_(
                    DuaEmployeeDemographics.fineos_customer_number == self.fineos_customer_number,
                ),
            )
            .join(
                DuaReportingUnit,
                and_(
                    DuaReportingUnit.organization_unit_id == OrganizationUnit.organization_unit_id,
                    DuaReportingUnit.dua_id
                    == DuaEmployeeDemographics.employer_reporting_unit_number,
                    DuaReportingUnit.employer_id == employer.employer_id,
                ),
            )
            .filter(
                OrganizationUnit.fineos_id.isnot(None),
            )
            .distinct()
        )

        return occupation_org_units_query.union(dua_demo_org_units_query).all()


class EmployeePushToFineosQueue(Base, TimestampMixin):
    """Employee records used by Eligibility Feed Export job to produces eligibility files containing employees for transfer to FINEOS"""

    __tablename__ = "employee_push_to_fineos_queue"

    __table_args__ = (Index("ix_priority_created_at", "priority", "created_at"),)

    employee_push_to_fineos_queue_id: Mapped[UUID] = Column(
        SQL_UUID(as_uuid=True),
        primary_key=True,
        default=uuid_gen,
        comment="Unique primary key id",
    )
    employee_id: Mapped[Optional[UUID]] = Column(
        SQL_UUID(as_uuid=True),
        index=True,
        comment="Internal id of employee",
    )
    action = Column(
        Text,
        index=True,
        comment="Action taking place on employee record",
    )
    modified_at = Column(
        TIMESTAMP(timezone=True),
        default=utc_timestamp_gen,
        comment="Time at which record was modified",
    )
    process_id = Column(
        Integer,
        index=True,
        comment="Process id of action on employee record",
    )
    employer_id: Mapped[Optional[UUID]] = Column(
        SQL_UUID(as_uuid=True),
        index=True,
        comment="Internal id of employer",
    )
    priority: Mapped[int] = Column(
        Integer,
        comment="Priority for processing from the queue",
        nullable=False,
        server_default=text("0"),
    )


class EmployeePubEftPair(Base, TimestampMixin):
    __tablename__ = "link_employee_pub_eft_pair"
    employee_id: Mapped[UUID] = Column(
        SQL_UUID(as_uuid=True), ForeignKey("employee.employee_id"), primary_key=True
    )
    pub_eft_id: Mapped[UUID] = Column(
        SQL_UUID(as_uuid=True), ForeignKey("pub_eft.pub_eft_id"), primary_key=True
    )

    employee = cast(Employee, relationship("Employee", back_populates="pub_efts"))
    pub_eft = cast(PubEft, relationship("PubEft", back_populates="employees"))


class AbsencePaidLeaveCase(Base, TimestampMixin):
    """
    Contains the benefit for the claimant and payments for the period of time.
    A leave request always has at least one Absence Paid Leave Case,
    but can have multiple when the leave request spans benefit years.
    """

    __tablename__ = "absence_paid_leave_case"

    absence_paid_leave_case_id: Mapped[UUID] = Column(
        SQL_UUID(as_uuid=True), primary_key=True, default=uuid_gen, nullable=False
    )
    absence_paid_leave_case_number: Mapped[str] = Column(
        Text, comment="Case Number from FINEOS", unique=True, nullable=False
    )
    leave_request_id: Mapped[UUID] = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey("leave_request.leave_request_id"),
        comment="ID associated with a leave request",
        index=True,
        nullable=False,
    )
    claim_id: Mapped[UUID] = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey("claim.claim_id"),
        index=True,
        comment="Related claim for payment",
        nullable=False,
    )
    start_date: Mapped[date] = Column(Date, nullable=False, comment="start of the paid leave")
    end_date: Mapped[date] = Column(Date, nullable=False, comment="end of the paid leave")
    average_weekly_wage = Column(
        Numeric(asdecimal=True),
        nullable=True,
        # Will be null for second Absence Paid Leave Case of a leave request if the leave request is not included in V_PAIDLEAVECASE_SOM
        comment="The AWW used to calculate the benefit for this absence paid leave case",
    )

    leave_request: Mapped["LeaveRequest"] = relationship(
        "LeaveRequest", back_populates="absence_paid_leave_cases"
    )
    claim: Mapped["Claim"] = relationship("Claim")


class LeaveRequest(Base, TimestampMixin):
    """A case of Paid leave absence"""

    __tablename__ = "leave_request"

    leave_request_id: Mapped[UUID] = Column(
        SQL_UUID(as_uuid=True), primary_key=True, default=uuid_gen, nullable=False
    )
    fineos_leave_request_id: Mapped[int] = Column(
        Integer, comment="FINEOS id for leave request", index=True, nullable=False
    )
    claim_id: Mapped[UUID] = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey("claim.claim_id"),
        index=True,
        comment="Related claim for payment",
        nullable=False,
    )
    absence_reason_id: Mapped[int] = Column(
        Integer, ForeignKey("lk_absence_reason.absence_reason_id"), nullable=False
    )
    leave_approval_decision_id = Column(
        Integer, ForeignKey("lk_leave_request_decision.leave_request_decision_id"), nullable=False
    )

    # Note that this column does not always contain accurate information
    # For example, if the financial eligibility decision was overridden in FINEOS,
    # the status here will not be updated.
    eligibility_decision_id = Column(
        Integer, ForeignKey("lk_eligibility_decision.eligibility_decision_id"), nullable=False
    )

    is_id_proofed = Column(Boolean, nullable=False)

    claim: Mapped["Claim"] = relationship("Claim")
    absence_reason: Mapped[LkAbsenceReason] = relationship(LkAbsenceReason)
    leave_approval_decision: Mapped[LkLeaveRequestDecision] = relationship(LkLeaveRequestDecision)
    eligibility_decision: Mapped[LkEligibilityDecision] = relationship(LkEligibilityDecision)
    absence_paid_leave_cases = cast(
        Optional[List["AbsencePaidLeaveCase"]],
        relationship("AbsencePaidLeaveCase", back_populates="leave_request"),
    )


class Claim(Base, TimestampIndexMixin):
    """A submitted claim for PFML benefits that represent/correspond to an Absence Case in Fineos"""

    __tablename__ = "claim"
    # https://ghost.n1zyy.com/pg_trgm-migrations-with-alembic/
    __table_args__ = (
        Index(
            "ix_claim_fineos_absence_id_trgm",
            "fineos_absence_id",
            postgresql_ops={"fineos_absence_id": "public.gin_trgm_ops"},
            postgresql_using="gin",
        ),
    )
    change_requests: Mapped[list["ChangeRequest"]] = relationship(back_populates="claim")

    claim_end_date = Column(
        Date,
        comment="End date of all absence periods",
    )
    claim_start_date = Column(
        Date,
        comment="Start date of all absence periods",
    )
    approval_date = Column(Date, nullable=True, comment="The date this claim was approved")
    authorized_representative_id = Column(
        SQL_UUID(as_uuid=True),
        comment="id of authorized representative associated with claim",
    )
    benefit_days = Column(
        Integer,
        comment="Number of days of benefit",
    )
    claim_id: Mapped[UUID] = Column(
        SQL_UUID(as_uuid=True),
        primary_key=True,
        default=uuid_gen,
        comment="Unique primary key id",
    )
    claim_type_id: Mapped[Optional[int]] = deprecated_column(
        Integer,
        ForeignKey("lk_claim_type.claim_type_id"),
        comment='id corresponding to an ClaimType enum, eg 1 ("Family Leave")',
    )
    employee_id: Mapped[Optional[UUID]] = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey("employee.employee_id"),
        index=True,
        comment="Internal id of employee associated with claim",
    )
    employer_id: Mapped[Optional[UUID]] = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey("employer.employer_id"),
        index=True,
        comment="Internal id of employer associated with claim",
    )
    fineos_absence_id = Column(
        Text,
        index=True,
        unique=True,
        comment="Associated Absence case ID",
    )
    fineos_absence_status_id = Column(
        Integer,
        ForeignKey("lk_absence_status.absence_status_id"),
        comment='id corresponding to an AbsenceStatus enum, eg 1 ("Adjudication")',
    )
    fineos_notification_id = Column(
        Text,
        comment="Internal id associated with fineos notification record for claim",
    )
    is_id_proofed: Mapped[Optional[bool]] = deprecated_column(
        Boolean,
        comment="Whether or not the employee asocciated with absence period is id proofed",
    )
    organization_unit_id = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey("organization_unit.organization_unit_id"),
        nullable=True,
        comment="Internal id for organization unit associated with claim",
    )
    has_paid_payments_persisted = Column(
        Boolean,
        nullable=True,
        comment="Whether or not claimant received payments for this claim",
    )
    claim_type: Mapped[Optional[LkClaimType]] = relationship(LkClaimType)
    fineos_absence_status: Mapped[Optional[LkAbsenceStatus]] = relationship(LkAbsenceStatus)
    employee: Mapped[Optional["Employee"]] = relationship("Employee", back_populates="claims")
    employer: Mapped[Optional["Employer"]] = relationship("Employer", back_populates="claims")
    state_logs: Mapped[list["StateLog"]] = relationship("StateLog", back_populates="claim")
    payments: DynamicMapped["Payment"] = relationship("Payment", back_populates="claim")
    managed_requirements = cast(
        Optional[List["ManagedRequirement"]],
        relationship("ManagedRequirement", back_populates="claim"),
    )
    absence_periods = cast(
        Optional[List["AbsencePeriod"]], relationship("AbsencePeriod", back_populates="claim")
    )
    leave_requests = cast(
        Optional[List["LeaveRequest"]], relationship("LeaveRequest", back_populates="claim")
    )
    organization_unit: Mapped[Optional[OrganizationUnit]] = relationship(OrganizationUnit)
    application: Mapped[Optional["Application"]] = relationship(back_populates="claim")
    appeal: Mapped[Optional["Appeal"]] = relationship(back_populates="claim")

    def has_concurrent_employers(self):
        if self.application:
            return self.application.has_concurrent_employers
        return None

    def percentage_hours_with_employer(self):
        if self.application:
            if (
                self.application.hours_worked_per_week
                and self.application.hours_worked_per_week_all_employers
            ):
                return Decimal(self.application.hours_worked_per_week) / Decimal(
                    self.application.hours_worked_per_week_all_employers
                )
        return None

    @hybrid_property
    def earliest_follow_up_date(self) -> Optional[date]:
        from massgov.pfml.db.lookup_data.employees import (
            ManagedRequirementStatus,
            ManagedRequirementType,
        )

        def _filter(requirement: ManagedRequirement) -> bool:
            valid_status = (
                requirement.managed_requirement_status_id
                == ManagedRequirementStatus.OPEN.managed_requirement_status_id
            )
            valid_type = (
                requirement.managed_requirement_type_id
                == ManagedRequirementType.EMPLOYER_CONFIRMATION.managed_requirement_type_id
            )
            not_expired = (
                requirement.follow_up_date is not None
                and requirement.follow_up_date >= date.today()
            )
            return valid_status and valid_type and not_expired

        if not self.managed_requirements:
            return None
        filtered_requirements = filter(_filter, self.managed_requirements)
        requirements = sorted(filtered_requirements, key=lambda x: x.follow_up_date)
        if len(requirements):
            return requirements[0].follow_up_date
        return None

    @earliest_follow_up_date.inplace.expression
    @classmethod
    def _earliest_follow_up_date_expression(cls):
        from massgov.pfml.db.lookup_data.employees import (
            ManagedRequirementStatus,
            ManagedRequirementType,
        )

        aliasManagedRequirement = aliased(ManagedRequirement)
        status_id = aliasManagedRequirement.managed_requirement_status_id
        type_id = aliasManagedRequirement.managed_requirement_type_id
        filters = and_(
            aliasManagedRequirement.claim_id == cls.claim_id,
            status_id == ManagedRequirementStatus.OPEN.managed_requirement_status_id,
            type_id == ManagedRequirementType.EMPLOYER_CONFIRMATION.managed_requirement_type_id,
            aliasManagedRequirement.follow_up_date >= date.today(),
        )
        return (
            select(func.min(aliasManagedRequirement.follow_up_date))
            .where(filters)
            .label("earliest_follow_up_date")
        )

    @hybrid_property
    def latest_follow_up_date(self) -> Optional[date]:
        """
        Note that this property (for use in our dashboard sorting),
        returns the latest_follow_up_date only when the requirement is not open,
        as well as a few other filters.
        """
        from massgov.pfml.db.lookup_data.employees import (
            ManagedRequirementStatus,
            ManagedRequirementType,
        )

        def _filter(requirement: ManagedRequirement) -> bool:
            not_open_status = (
                requirement.managed_requirement_status_id
                != ManagedRequirementStatus.OPEN.managed_requirement_status_id
            )
            valid_type = (
                requirement.managed_requirement_type_id
                == ManagedRequirementType.EMPLOYER_CONFIRMATION.managed_requirement_type_id
            )
            expired = (
                requirement.follow_up_date is not None and requirement.follow_up_date < date.today()
            )
            return valid_type and (not_open_status or expired)

        if not self.managed_requirements:
            return None
        filtered_requirements = filter(_filter, self.managed_requirements)
        requirements = sorted(filtered_requirements, key=lambda x: x.follow_up_date, reverse=True)
        if len(requirements):
            return requirements[0].follow_up_date
        return None

    @latest_follow_up_date.inplace.expression
    @classmethod
    def _latest_follow_up_date_expression(cls):
        from massgov.pfml.db.lookup_data.employees import (
            ManagedRequirementStatus,
            ManagedRequirementType,
        )

        aliasManagedRequirement = aliased(ManagedRequirement)
        type_id = aliasManagedRequirement.managed_requirement_type_id
        status_id = aliasManagedRequirement.managed_requirement_status_id

        filters = and_(
            aliasManagedRequirement.claim_id == cls.claim_id,
            type_id == ManagedRequirementType.EMPLOYER_CONFIRMATION.managed_requirement_type_id,
            or_(
                status_id != ManagedRequirementStatus.OPEN.managed_requirement_status_id,
                aliasManagedRequirement.follow_up_date < date.today(),
            ),
        )
        return (
            select(func.max(aliasManagedRequirement.follow_up_date))
            .where(filters)
            .label("latest_follow_up_date")
        )

    @property
    def claim_type_description(self) -> Optional[str]:
        from massgov.pfml.db.lookup_data.employees import (
            ABSENCE_REASON_ID_CLAIM_TYPE_ID_MAPPING,
            ClaimType,
        )

        earliest_absence = self.earliest_absence_period

        if earliest_absence is None or earliest_absence.absence_reason_id is None:
            return None
        claim_type_id = ABSENCE_REASON_ID_CLAIM_TYPE_ID_MAPPING[earliest_absence.absence_reason_id]
        return ClaimType.get_description(claim_type_id)

    # TODO:
    # rename to is_id_proofed once feature is fully
    @property
    def get_is_id_proofed(self) -> bool:
        # TODO PFMLPB-14612: this is only used in one place in the codebase, the PaymentExtractStep.
        # We will move this logic to that step and remove this property in PFMLPB-14612.
        # We are temporarily leaving this in to keep the diff as small as possible.
        leave_request: Optional[LeaveRequest] = (
            get_object_session(self)
            .query(LeaveRequest)
            .filter(LeaveRequest.claim_id == self.claim_id)
            .filter(LeaveRequest.is_id_proofed.is_(True))
            .first()
        )
        if leave_request is None:
            return False
        return True

    @property
    def has_prepaid_card_payment_preference(self) -> bool:
        return (
            self.application is not None
            and self.application.payment_preference is not None
            and self.application.payment_preference.payment_method is not None
            and self.application.payment_preference.payment_method.payment_method_description
            == "Prepaid Card"
        )

    @property
    def does_claim_span_benefit_years(self) -> Optional[bool]:
        """
        Whether or not this claim has dates that span benefit years.
        A claim spans benefit years, the start date is before the start of a benefit year
        or if the end date is after the end of a benefit year.
        """
        from massgov.pfml.api.services.applications import get_crossed_benefit_years

        claim_start_date = self.absence_periods_earliest_start_date
        claim_end_date = self.absence_periods_latest_end_date

        db_session = get_object_session(self)

        if (self.employee_id and claim_start_date and claim_end_date) and get_crossed_benefit_years(
            self.employee_id, claim_start_date, claim_end_date, db_session
        ):
            return True
        return False

    @property
    def employee_tax_identifier(self) -> Optional[str]:
        if not self.employee:
            return None

        if self.employee.tax_identifier is None:
            return None

        return self.employee.tax_identifier.tax_identifier

    @property
    def employer_fein(self) -> Optional[str]:
        if not self.employer:
            return None

        return self.employer.employer_fein

    @property
    def needs_payment_calculation(self) -> bool:
        """
        Determines whether or not we need to run a payments sub-query to populate
        `has_paid_payments` data for this claim.
        """

        if self.has_paid_payments_persisted is True:
            # We've already found payments for this claim and persisted that result
            # no need to calculate it again
            return False

        # We either haven't calculated this data before, or ran the sub-query and didn't find any payments
        # either way, we need to run a sub-query to populate `has_paid_payments`
        return True

    @property
    def has_approval_status(self) -> bool:
        """
        Whether or not this claim has a status of "Approved".
        Not for use in database queries!

        This is a proxy for whether or not the claim is approved.
        If the claim has a status of "Approved", then we know it has been approved.
        If the claim does NOT have a status of "Approved", then it may have been approved
        and we just haven't gotten the update from FINEOS yet. (via extracts, etc.)
        """

        if not self.fineos_absence_status:
            return False

        from massgov.pfml.db.lookup_data.absences import AbsenceStatus

        return (
            self.fineos_absence_status.absence_status_id == AbsenceStatus.APPROVED.absence_status_id
        )

    @property
    def earliest_absence_period(self) -> Optional["AbsencePeriod"]:
        if not self.absence_periods:
            return None
        return sorted(
            self.absence_periods, key=lambda period: (period.start_date is None, period.start_date)
        )[0]

    @property
    def approved_absence_periods(self) -> Optional[List["AbsencePeriod"]]:
        if not self.absence_periods:
            return None

        return [period for period in self.absence_periods if period.is_approved]

    @property
    def absence_periods_earliest_start_date(self) -> Optional[date]:
        earliest_absence_period = self.earliest_absence_period
        return (
            earliest_absence_period.absence_period_start_date if earliest_absence_period else None
        )

    @property
    def earliest_approved_start_date(self) -> Optional[date]:
        if not self.approved_absence_periods:
            return None

        return sorted(
            self.approved_absence_periods,
            key=lambda period: (period.start_date is None, period.start_date),
        )[0].absence_period_start_date

    @property
    def absence_periods_latest_end_date(self) -> Optional[date]:
        if not self.absence_periods:
            return None

        absence_period_end_dates = [
            absence_period.absence_period_end_date
            for absence_period in self.absence_periods
            if absence_period.absence_period_end_date
        ]
        return max(absence_period_end_dates, default=None)

    @property
    def prior_year(self) -> Optional[date]:
        # Get the previous year from the claim's absence period earliest start date
        prior_year = None
        earliest_start_date = self.absence_periods_earliest_start_date
        if earliest_start_date is not None:
            prior_year = earliest_start_date - timedelta(weeks=52)

        return prior_year

    @property
    def is_reviewable(self) -> bool:
        """
        Whether or not this claim is reviewable by an employer.

        To be reviewable means that it has an associated open requirement
        (earliest_follow_up_date isn't None)
        AND the claim has at least one reviewable (non-final) absence period request decision
        """

        if self.earliest_follow_up_date is None or self.absence_periods is None:
            return False

        has_reviewable_absence_period = any(
            [ap.has_final_decision is False for ap in self.absence_periods]
        )
        if not has_reviewable_absence_period:
            return False

        return True

    @property
    def documents(self) -> List["Document"]:
        if not self.application:
            return []

        return self.application.documents

    @property
    def has_extensions(self) -> bool:
        # FINEOS creates an additional LeaveRequest everytime there is an extension created
        leave_request_count = len(
            get_object_session(self)
            .query(LeaveRequest)
            .filter(LeaveRequest.claim_id == self.claim_id)
            .all()
        )
        return leave_request_count > 1

    @property
    def approved_intermittent_absence_periods(self) -> Optional[List["AbsencePeriod"]]:
        """
        Returns only the approved and intermittent absence periods for this claim.
        Useful for reporting intermittent leave hours to this claim.
        """

        if not self.absence_periods:
            return None

        return list(
            filter(
                lambda absence_period: (absence_period.is_approved_and_intermittent),
                self.absence_periods,
            )
        )

    @property
    def is_out_of_intake(self) -> bool:
        from massgov.pfml.db.lookup_data.absences import AbsenceStatus

        return self.fineos_absence_status_id in [
            AbsenceStatus.ADJUDICATION.absence_status_id,
            AbsenceStatus.APPROVED.absence_status_id,
            AbsenceStatus.CLOSED.absence_status_id,
            AbsenceStatus.COMPLETED.absence_status_id,
            AbsenceStatus.DECLINED.absence_status_id,
        ]


class BenefitYear(Base, TimestampMixin):
    """Claimant's benefit year"""

    __tablename__ = "benefit_year"

    benefit_year_id: Mapped[UUID] = Column(
        SQL_UUID(as_uuid=True), primary_key=True, default=uuid_gen, comment="Internal primary key"
    )

    employee_id: Mapped[UUID] = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey("employee.employee_id"),
        nullable=False,
        index=True,
        comment="Associated employee",
    )

    start_date: Mapped[date] = Column(Date, nullable=False, comment="Start date of benefit year")

    end_date: Mapped[date] = Column(Date, nullable=False, comment="End date of benefit year")

    # The base period used to calculate IAWW
    # in order to recalculate IAWW for other employers
    base_period_start_date: Mapped[date] = Column(
        Date, nullable=False, comment="Start date of base period of benefit year"
    )

    base_period_end_date: Mapped[date] = Column(
        Date, nullable=False, comment="End date of base period of benefit year"
    )

    total_wages: Mapped[Decimal | None] = Column(
        Numeric(asdecimal=True),
        comment="Total wages earned in base period when benefit year was created",
    )

    Index("uix_employee_id_start_date", employee_id, start_date, unique=True)

    employee: Mapped[Employee] = relationship(Employee, back_populates="benefit_years")

    contributions: Mapped[List["BenefitYearContribution"]] = relationship(
        "BenefitYearContribution", back_populates="benefit_year", cascade="all, delete-orphan"
    )

    @hybrid_property
    def current_benefit_year(self) -> bool:
        today = date.today()
        return today >= self.start_date and today <= self.end_date

    @current_benefit_year.inplace.expression
    @classmethod
    def _current_benefit_year_expression(cls) -> ColumnElement[bool]:
        return func.now().between(cls.start_date, cls.end_date)

    @property
    def base_period_start_quarter(self) -> Quarter:
        return Quarter.from_date(self.base_period_start_date)

    @property
    def base_period_end_quarter(self) -> Quarter:
        return Quarter.from_date(self.base_period_end_date)


class BenefitYearContribution(Base, TimestampMixin):
    """AWW to use for claims made by an employee against an employer in a benefit year"""

    __tablename__ = "benefit_year_contribution"
    benefit_year_contribution_id = Column(
        SQL_UUID(as_uuid=True), primary_key=True, default=uuid_gen, comment="Internal primary key"
    )

    benefit_year_id = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey("benefit_year.benefit_year_id"),
        nullable=False,
        index=True,
        comment="Associated benefit year",
    )
    benefit_year: Mapped["BenefitYear"] = relationship(
        "BenefitYear", back_populates="contributions"
    )

    employer_id = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey("employer.employer_id"),
        nullable=False,
        index=True,
        comment="Associated employer",
    )
    employer: Mapped["Employer"] = relationship(
        "Employer", back_populates="employee_benefit_year_contributions"
    )

    employee_id: Mapped[UUID] = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey("employee.employee_id"),
        nullable=False,
        index=True,
        comment="Associated employer",
    )
    employee: Mapped["Employee"] = relationship(
        "Employee", back_populates="employer_benefit_year_contributions"
    )

    average_weekly_wage: Mapped[Decimal] = Column(
        Numeric(asdecimal=True),
        nullable=False,
        comment="AWW for claims against this employer by this employee in this benefit year",
    )

    q1_wages_id = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey("wages_and_contributions_history.wages_and_contributions_history_id"),
        comment="Wages from this employer in Q1 of base period",
    )
    q1_wages: Mapped[Optional["WagesAndContributionsHistory"]] = relationship(
        "WagesAndContributionsHistory", foreign_keys=[q1_wages_id]
    )

    q2_wages_id = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey("wages_and_contributions_history.wages_and_contributions_history_id"),
        comment="Wages from this employer in Q2 of base period",
    )
    q2_wages: Mapped[Optional["WagesAndContributionsHistory"]] = relationship(
        "WagesAndContributionsHistory", foreign_keys=[q2_wages_id]
    )

    q3_wages_id = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey("wages_and_contributions_history.wages_and_contributions_history_id"),
        comment="Wages from this employer in Q3 of base period",
    )
    q3_wages: Mapped[Optional["WagesAndContributionsHistory"]] = relationship(
        "WagesAndContributionsHistory", foreign_keys=[q3_wages_id]
    )

    q4_wages_id = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey("wages_and_contributions_history.wages_and_contributions_history_id"),
        comment="Wages from this employer in Q4 of base period",
    )
    q4_wages: Mapped[Optional["WagesAndContributionsHistory"]] = relationship(
        "WagesAndContributionsHistory", foreign_keys=[q4_wages_id]
    )

    is_aww_overridden: Mapped[Optional[bool]] = Column(
        Boolean,
        comment="Flag if the AWW for this BenefitYearContribution was overridden with a new value from FINEOS",
    )

    Index(
        "ix_benefit_year_id_employer_id_employee_id",
        benefit_year_id,
        employer_id,
        employee_id,
        unique=True,
    )

    @property
    def wages(self) -> Tuple[Optional[UUID], Optional[UUID], Optional[UUID], Optional[UUID]]:
        return (self.q1_wages_id, self.q2_wages_id, self.q3_wages_id, self.q4_wages_id)


class Payment(Base, TimestampIndexMixin):
    """A payment under the PFML program."""

    __tablename__ = "payment"
    payment_id: Mapped[UUID] = Column(
        SQL_UUID(as_uuid=True), primary_key=True, default=uuid_gen, comment="Internal primary key"
    )
    claim_id = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey("claim.claim_id"),
        index=True,
        comment="Related claim for payment",
    )
    # Attach the employee ID as well as some payments aren't associated with a claim
    employee_id: Mapped[Optional[UUID]] = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey("employee.employee_id"),
        index=True,
        comment="Related employee for payment",
    )
    payment_transaction_type_id = Column(
        Integer,
        ForeignKey("lk_payment_transaction_type.payment_transaction_type_id"),
        comment="Payment transaction type",
    )
    payment_relevant_party_id = Column(
        Integer,
        ForeignKey("lk_payment_relevant_party.payment_relevant_party_id"),
        comment="Second party for payment (whether payee or payer)",
    )
    period_start_date = Column(Date, comment="Pay period start date")
    period_end_date = Column(Date, comment="Pay period end date")
    payment_date = Column(Date, comment="Payment issued date")
    absence_case_creation_date = Column(Date, comment="Absence case creation date")
    amount: Mapped[Decimal] = Column(
        Numeric(asdecimal=True), nullable=False, comment="Payment amount"
    )
    fineos_pei_c_value = Column(Text, index=True, comment="FINEOS database C key value")
    fineos_pei_i_value = Column(Text, index=True, comment="FINEOS database I key value")
    is_adhoc_payment = Column(
        Boolean, default=False, server_default="FALSE", comment="Is ad hoc payment issued by DFML"
    )
    fineos_extraction_date = Column(Date, comment="Date payment was received from FINEOS extracts")

    # Backfilled legacy MMARS payments use this for the paid date, all new payments leave null
    disb_check_eft_issue_date: Mapped[Optional[Date]] = deprecated_column(
        Date, comment="Date payment was paid if a legacy MMARS payment"
    )

    disb_method_id = Column(
        Integer,
        ForeignKey("lk_payment_method.payment_method_id"),
        comment="Disbursement method for payment (check, etc)",
    )
    leave_request_decision: Mapped[Optional[str]] = deprecated_column(
        Text,
        comment="Decision (e.g., pending, approved) of the leave request associated with the payment",
    )
    experian_address_pair_id = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey("link_experian_address_pair.fineos_address_id"),
        index=True,
        comment="Address associated with the payment",
    )
    has_address_update: Mapped[Optional[Boolean]] = deprecated_column(
        Boolean,
        default=False,
        server_default="FALSE",
        nullable=False,
        comment="True if the address for the payment is different from the employee's previous address",
    )
    has_eft_update: Mapped[Optional[Boolean]] = deprecated_column(
        Boolean,
        default=False,
        server_default="FALSE",
        nullable=False,
        comment="True if the EFT details for the payment are different from the employee's previous EFT details",
    )

    fineos_extract_import_log_id = Column(
        Integer,
        ForeignKey("import_log.import_log_id"),
        index=True,
        comment="ID of import log that created this payment from FINEOS extracts",
    )
    pub_eft_id = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey("pub_eft.pub_eft_id"),
        comment="Associated EFT information for payment",
    )
    payment_individual_id_seq: Sequence = Sequence("payment_individual_id_seq")
    pub_individual_id = Column(
        Integer,
        payment_individual_id_seq,
        index=True,
        server_default=payment_individual_id_seq.next_value(),
        comment="Unique ID used to track payments sent to M&T bank",
    )
    claim_type_id = Column(
        Integer,
        ForeignKey("lk_claim_type.claim_type_id"),
        comment="Claim type of the associated claim for payment",
    )

    ####

    vpei_id = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey("fineos_extract_vpei.vpei_id"),
        comment="Associated FINEOS extract that generated this payment",
    )
    # Defined in models/payments.py due to a circular import error
    vpei: Mapped[Optional["FineosExtractVpei"]] = relationship(back_populates="payment")

    has_active_writeback_issue = Column(
        Boolean,
        default=False,
        server_default="FALSE",
        nullable=False,
        comment="If this is a duplicate payment that's already been processed or is in processing",
    )

    #####

    fineos_leave_request_id: Mapped[Optional[int]] = deprecated_column(
        Integer,
        index=True,
        comment="The FINEOS ID of the leave request associated with the payment",
    )
    fineos_employee_first_name = Column(
        Text, comment=column_doc("First name of the associated employee in FINEOS", pii=True)
    )
    fineos_employee_middle_name = Column(
        Text, comment=column_doc("Middle name of the associated employee in FINEOS", pii=True)
    )
    fineos_employee_last_name = Column(
        Text, comment=column_doc("Last name of the associated employee in FINEOS", pii=True)
    )

    payee_name = Column(Text, comment="The employer name, if an employer reimbursement payment")

    latest_state_log: Mapped[Optional["LatestStateLog"]] = relationship(
        "LatestStateLog", back_populates="payment"
    )
    claim: Mapped[Optional["Claim"]] = relationship("Claim", back_populates="payments")
    employee: Mapped[Optional["Employee"]] = relationship("Employee")
    claim_type: Mapped[Optional[LkClaimType]] = relationship(LkClaimType)
    payment_transaction_type: Mapped[Optional[LkPaymentTransactionType]] = relationship(
        LkPaymentTransactionType
    )
    payment_relevant_party: Mapped[Optional[LkPaymentRelevantParty]] = relationship(
        LkPaymentRelevantParty
    )
    disb_method: Mapped[Optional[LkPaymentMethod]] = relationship(
        LkPaymentMethod, foreign_keys=disb_method_id
    )
    pub_eft: Mapped[Optional[PubEft]] = relationship(PubEft)
    experian_address_pair: Mapped[Optional[ExperianAddressPair]] = relationship(
        ExperianAddressPair, foreign_keys=experian_address_pair_id
    )
    fineos_extract_import_log: Mapped[Optional["ImportLog"]] = relationship("ImportLog")
    state_logs: Mapped[list["StateLog"]] = relationship("StateLog", back_populates="payment")
    payment_details: Mapped[List["PaymentDetails"]] = relationship(
        "PaymentDetails", back_populates="payment", order_by="PaymentDetails.period_start_date"
    )

    check: Mapped[Optional["PaymentCheck"]] = relationship(
        "PaymentCheck", backref="payment", uselist=False
    )

    # Defined in models/payments.py due to a circular import error
    payment_lines: Mapped[list["PaymentLine"]] = relationship(back_populates="payment")
    fineos_writeback_details: Mapped[list["FineosWritebackDetails"]] = relationship(
        back_populates="payment"
    )
    reference_files: Mapped[list["PaymentReferenceFile"]] = relationship(back_populates="payment")

    def sorted_fineos_writeback_details(self):
        """Returns a copy of fineos_writeback_details that is sorted by created_at"""
        return sorted(self.fineos_writeback_details, key=lambda x: x.created_at)


class PaymentDetails(Base, TimestampMixin):
    """Provides a breakdown of a payment into the corresponding pay periods.  Contains the actual amount paid to a claimant as well as the amount before taxes and split payments are taken out.  Raw data is stored in vpei_payment_details"""

    __tablename__ = "payment_details"
    __table_status__ = TableStatus.ACTIVE

    payment_details_id: Mapped[UUID] = Column(
        SQL_UUID(as_uuid=True),
        primary_key=True,
        default=uuid_gen,
        comment="Internal primary identifier",
    )
    payment_id: Mapped[UUID] = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey(Payment.payment_id),
        nullable=False,
        index=True,
        comment="The payment this detail is associated with",
    )

    payment_details_c_value = Column(
        Text,
        index=True,
        comment="The FINEOS class value. Combined with the I value for a unique identifier",
    )
    payment_details_i_value = Column(
        Text,
        index=True,
        comment="The FINEOS index value. Combined with the C value for a unique identifier",
    )

    vpei_payment_details_id = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey("fineos_extract_vpei_payment_details.vpei_payment_details_id"),
        comment="The raw row that the payment detail came from",
    )

    period_start_date = Column(Date, comment="The start date for the payment detail")
    period_end_date = Column(Date, comment="The end date for the payment detail")
    amount: Mapped[Decimal] = Column(
        Numeric(asdecimal=True), nullable=False, comment="The amount for the payment detail"
    )
    business_net_amount: Mapped[Decimal] = Column(
        Numeric(asdecimal=True),
        nullable=False,
        comment="The amount before taxes and employer reimbursements are taken out",
    )

    payment: Mapped[Payment] = relationship(Payment, back_populates="payment_details")

    # Defined in models/payments.py due to a circular import error
    payment_lines: Mapped[list["PaymentLine"]] = relationship(back_populates="payment_details")


class PaymentCheck(Base, TimestampMixin):
    """A Check used for payment"""

    __tablename__ = "payment_check"
    payment_id = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey(Payment.payment_id),
        primary_key=True,
        comment="ID associated with payment",
    )
    check_number = Column(
        Integer,
        nullable=False,
        index=True,
        unique=True,
        comment="Unique number associated with check",
    )
    check_posted_date = Column(Date, comment="Date check was posted")
    payment_check_status_id = Column(
        Integer,
        ForeignKey("lk_payment_check_status.payment_check_status_id"),
        comment="ID of status of check payment",
    )

    payment_check_status: Mapped[Optional[LkPaymentCheckStatus]] = relationship(
        LkPaymentCheckStatus
    )


class AuthorizedRepEmployee(Base, TimestampMixin):
    __tablename__ = "link_authorized_rep_employee"
    authorized_representative_id = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey("authorized_representative.authorized_representative_id"),
        primary_key=True,
    )
    employee_id = Column(
        SQL_UUID(as_uuid=True), ForeignKey("employee.employee_id"), primary_key=True
    )

    authorized_rep: Mapped["AuthorizedRepresentative"] = relationship(
        "AuthorizedRepresentative", back_populates="employees"
    )
    employee: Mapped["Employee"] = relationship("Employee", back_populates="authorized_reps")


class User(Base, TimestampMixin):
    __tablename__ = "user"
    user_id: Mapped[UUID] = Column(SQL_UUID(as_uuid=True), primary_key=True, default=uuid_gen)
    email_address = Column(Text, unique=True)
    consented_to_data_sharing = Column(Boolean, default=False, nullable=False)
    first_name = Column(Text)
    last_name = Column(Text)
    phone_number = Column(Text)  # Formatted in E.164
    phone_extension = Column(Text)

    language_id = Column(
        Integer,
        ForeignKey("lk_language.language_id"),
        nullable=True,
        comment='id corresponding to a Language enum, eg 1 ("English")',
    )

    consented_to_view_tax_documents = Column(Boolean, nullable=True)
    auth_id = Column(Text, index=True, unique=True, nullable=True)

    roles: Mapped[list["LkRole"]] = relationship("LkRole", secondary="link_user_role", uselist=True)

    user_leave_administrators: Mapped[list["UserLeaveAdministrator"]] = relationship(
        "UserLeaveAdministrator", back_populates="user", uselist=True
    )
    user_leave_administrators_query: DynamicMapped["UserLeaveAdministrator"] = relationship(
        "UserLeaveAdministrator", viewonly=True
    )

    # the Employers the User is a Leave Admin for
    employers: Mapped[list["Employer"]] = relationship(
        "Employer", secondary="link_user_leave_administrator", uselist=True, viewonly=True
    )
    employers_query: DynamicMapped["Employer"] = relationship(
        "Employer", secondary="link_user_leave_administrator", viewonly=True
    )
    language: Mapped[Optional[LkLanguage]] = relationship(LkLanguage)

    def get_user_leave_admin_for_employer(
        self, employer: Employer
    ) -> Optional["UserLeaveAdministrator"]:
        """
        Return the user_leave_admin record for a given employer.
        This method does not filter out records based on activation or verification state.
        """
        return self.user_leave_administrators_query.filter(
            UserLeaveAdministrator.employer_id == employer.employer_id
        ).first()

    def get_leave_admin_notifications(self) -> list[str]:
        """
        Check if notifications were sent to this leave admin
        in the last 24 hours, to prevent leave admins
        with notifications, but no org units assigned,
        from seeing a blank page in the employer dashboard
        """
        # This is imported here to prevent circular import error
        from massgov.pfml.db.models.notifications import Notification
        from massgov.pfml.fineos.common import FineosRecipientType

        a_day_ago = utcnow() - timedelta(days=1)
        notifications = (
            get_object_session(self)
            .query(Notification.fineos_absence_id)
            # Filtering by date first lowers query execution cost substantially
            .filter(Notification.created_at > a_day_ago)
            .filter(
                Notification.request_json.contains(
                    {
                        "recipients": [{"email_address": self.email_address}],
                        "recipient_type": FineosRecipientType.LEAVE_ADMINISTRATOR,
                    }
                )
            )
            .distinct()
        )
        # claims that this leave admin was notified about in the last 24 hours
        return [n.fineos_absence_id for n in notifications]

    @property
    def active_employers_query(self) -> "Query[Employer]":
        return self.employers_query.filter(
            UserLeaveAdministrator.deactivated.isnot(True),
        )

    @property
    def submitted_applications(self) -> "Optional[List[Application]]":
        # This is imported here to prevent circular import error
        from massgov.pfml.db.models.applications import Application

        return (
            get_object_session(self)
            .query(Application)
            .filter(
                Application.user_id == self.user_id,
                Application.submitted_time != None,  # noqa: E711
            )
            .all()
        )

    @property
    def verified_employers_query(self) -> "Query[Employer]":
        return self.employers_query.filter(
            UserLeaveAdministrator.verification_id.isnot(None),
            UserLeaveAdministrator.deactivated.isnot(True),
        )

    @property
    def verified_employers(self) -> list[Employer]:
        return self.verified_employers_query.all()

    @property
    def verified_leave_admins_query(self) -> "Query[UserLeaveAdministrator]":
        return self.user_leave_administrators_query.filter(
            UserLeaveAdministrator.verification_id.isnot(None),
            UserLeaveAdministrator.deactivated.isnot(True),
        )

    @property
    def verified_leave_admins(self) -> list["UserLeaveAdministrator"]:
        return self.verified_leave_admins_query.all()

    def is_verified_leave_admin_for_employer(self, employer: Employer) -> bool:
        return bool(
            self.verified_leave_admins_query.filter(
                UserLeaveAdministrator.employer_id == employer.employer_id
            ).count()
        )

    def _has_role_in(self, accepted_roles: List[LkRole]) -> bool:
        accepted_role_ids = set(role.role_id for role in accepted_roles)
        for role in self.roles:
            if role.role_id in accepted_role_ids:
                return True

        return False

    @property
    def is_employer(self) -> bool:
        from massgov.pfml.db.lookup_data.employees import Role

        return self._has_role_in([Role.EMPLOYER])

    @property
    def is_fineos(self) -> bool:
        from massgov.pfml.db.lookup_data.employees import Role

        return self._has_role_in([Role.FINEOS])

    @property
    def is_idp_user(self) -> bool:
        from massgov.pfml.db.lookup_data.employees import Role

        return self._has_role_in([Role.IDP])

    @property
    def is_pfml_crm_user(self) -> bool:
        from massgov.pfml.db.lookup_data.employees import Role

        return self._has_role_in([Role.PFML_CRM])

    @property
    def is_worker_user(self) -> bool:
        """
        Currently we do not populate a role for worker account users
        so for now we can rely on roles being empty to extrapolate

        See: create_user in users.py utility
        """
        return not self.roles

    @property
    def full_name(self) -> Optional[str]:
        if not self.first_name:
            return None
        return f"{self.first_name} {self.last_name}"

    @property
    def has_multiple_tax_identifiers(self) -> bool:
        from massgov.pfml.db.models.applications import Application

        # A claimant is considered to have multiple tax identifiers if they have multiple submitted applications with
        # different tax identifiers / ssn.
        # A claimant is NOT considered to have multiple tax identifiers if any of the following is true:
        # - In addition to 1 or more submitted applications, they have 1 or more incomplete applications with a null/empty
        #   tax identifiers (meaning they have left that field empty)
        # - In addition to 1 or more submitted applications, they have 1 or more incomplete applications with a different
        #   tax identifiers
        ssn_count = (
            get_object_session(self)
            .query(Application.tax_identifier_id)
            .filter(
                Application.user_id == self.user_id,
                Application.submitted_time != None,  # noqa: E711
                Application.tax_identifier_id != None,  # noqa: E711
            )
            .group_by(Application.tax_identifier_id)
            .count()
        )

        return ssn_count > 1


class UserRole(Base, TimestampMixin):
    __tablename__ = "link_user_role"
    user_id = Column(SQL_UUID(as_uuid=True), ForeignKey("user.user_id"), primary_key=True)
    role_id = Column(Integer, ForeignKey("lk_role.role_id"), primary_key=True)

    user: Mapped[User] = relationship(User, overlaps="roles")
    role: Mapped[LkRole] = relationship(LkRole, overlaps="roles")


class LkUserLeaveAdministratorActionType(Base):
    __tablename__ = "lk_user_leave_administrator_action_type"
    user_leave_administrator_action_type_id: Mapped[int] = Column(
        Integer, primary_key=True, autoincrement=True
    )
    user_leave_administrator_action_type_description: Mapped[str] = Column(Text, nullable=False)

    def __init__(
        self,
        user_leave_administrator_action_type_id,
        user_leave_administrator_action_type_description,
    ):
        self.user_leave_administrator_action_type_id = user_leave_administrator_action_type_id
        self.user_leave_administrator_action_type_description = (
            user_leave_administrator_action_type_description
        )


class UserLeaveAdministratorOrgUnit(Base, TimestampMixin):
    __tablename__ = "link_user_leave_administrator_org_unit"
    user_leave_administrator_id: Mapped[UUID] = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey("link_user_leave_administrator.user_leave_administrator_id"),
        primary_key=True,
    )
    organization_unit_id: Mapped[UUID] = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey("organization_unit.organization_unit_id"),
        primary_key=True,
    )

    organization_unit: Mapped[OrganizationUnit] = relationship(OrganizationUnit)

    def __init__(self, user_leave_administrator_id, organization_unit_id):
        self.user_leave_administrator_id = user_leave_administrator_id
        self.organization_unit_id = organization_unit_id


class UserLeaveAdministrator(Base, TimestampMixin):
    __tablename__ = "link_user_leave_administrator"
    __table_args__ = (UniqueConstraint("user_id", "employer_id"),)
    user_leave_administrator_id: Mapped[UUID] = Column(
        SQL_UUID(as_uuid=True), primary_key=True, default=uuid_gen
    )
    user_id: Mapped[UUID] = Column(
        SQL_UUID(as_uuid=True), ForeignKey("user.user_id"), nullable=False
    )
    employer_id: Mapped[UUID] = Column(
        SQL_UUID(as_uuid=True), ForeignKey("employer.employer_id"), nullable=False
    )
    fineos_web_id: Mapped[Optional[str]] = Column(Text)
    verification_id: Mapped[Optional[UUID]] = Column(
        SQL_UUID(as_uuid=True), ForeignKey("verification.verification_id"), nullable=True
    )
    deactivated: Mapped[bool] = Column(
        Boolean, default=False, server_default="FALSE", nullable=False
    )

    user: Mapped[User] = relationship(User, back_populates="user_leave_administrators")
    employer: Mapped[Employer] = relationship(
        Employer, back_populates="user_leave_administrators", uselist=False
    )
    verification: Mapped[Optional[Verification]] = relationship(Verification)

    user_leave_admin_actions: Mapped[List["UserLeaveAdministratorAction"]] = relationship(
        "UserLeaveAdministratorAction", back_populates="recipient_user_leave_administrator"
    )
    user_leave_admin_actions_query: DynamicMapped["UserLeaveAdministratorAction"] = relationship(
        "UserLeaveAdministratorAction",
        back_populates="recipient_user_leave_administrator",
        viewonly=True,
    )

    def user_leave_administrator_added_by_action(self) -> Optional["UserLeaveAdministratorAction"]:
        """
        Return the latest ADD action for a given user leave administrator, i.e. the
        last individual to invite this user to the organization.

        Do NOT return anything if the leave administrator record is not verified. This indicates an
        edge case where it was previously deactivated, and the user added the employer back to their
        account without fully verifying themselves. In this scenario, we would still have actions recorded,
        but they are outdated.
        """
        from massgov.pfml.db.lookup_data.employees import UserLeaveAdministratorActionType

        if not self.verified:
            return None

        return (
            self.user_leave_admin_actions_query.filter(
                UserLeaveAdministratorAction.user_leave_administrator_action_type_id
                == UserLeaveAdministratorActionType.ADD.user_leave_administrator_action_type_id
            )
            .order_by(UserLeaveAdministratorAction.user_leave_administrator_action_id.desc())
            .first()
        )

    @property
    def added_by(self) -> Optional[User]:
        action = self.user_leave_administrator_added_by_action()

        if not action:
            return None

        return action.user

    @property
    def added_at(self) -> Optional[datetime]:
        action = self.user_leave_administrator_added_by_action()

        if not action:
            return None

        return action.created_at

    @property
    def organization_units(self) -> list[OrganizationUnit]:
        return (
            get_object_session(self)
            .query(OrganizationUnit)
            .join(UserLeaveAdministratorOrgUnit)
            .filter(
                UserLeaveAdministratorOrgUnit.user_leave_administrator_id
                == self.user_leave_administrator_id,
                OrganizationUnit.employer_id == self.employer_id,
                OrganizationUnit.fineos_id.isnot(None),
            )
            .all()
        )

    @hybrid_property
    def has_fineos_registration(self) -> bool:
        """Indicates whether the leave admin exists in Fineos yet, signalling if Fineos
        API calls can be called on behalf of this leave admin yet"""
        return bool(self.fineos_web_id)

    @has_fineos_registration.inplace.expression
    @classmethod
    def _has_fineos_registration_expression(cls) -> ColumnElement[bool]:
        return cls.fineos_web_id.isnot(None)

    @hybrid_property
    def verified(self) -> bool:
        return bool(self.verification_id)

    @verified.inplace.expression
    @classmethod
    def _verified_expression(cls) -> ColumnElement[bool]:
        return cls.verification_id.isnot(None)

    @property
    def verified_at(self) -> Optional[datetime]:
        return deepgetattr(self, "verification.created_at")

    @property
    def verification_type(self) -> Optional[str]:
        return deepgetattr(self, "verification.verification_type.verification_type_description")


class UserLeaveAdministratorAction(Base, TimestampMixin):
    __tablename__ = "user_leave_administrator_action"
    user_leave_administrator_action_id: Mapped[UUID] = Column(
        SQL_UUID(as_uuid=True), primary_key=True, default=uuid_gen
    )
    user_id: Mapped[UUID] = Column(SQL_UUID(as_uuid=True), ForeignKey(User.user_id), nullable=False)
    recipient_email: Mapped[str] = Column(Text, nullable=False)
    recipient_employer_id: Mapped[UUID] = Column(
        SQL_UUID(as_uuid=True), ForeignKey(Employer.employer_id), nullable=False
    )
    recipient_user_leave_administrator_id: Mapped[Optional[UUID]] = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey(UserLeaveAdministrator.user_leave_administrator_id),
    )
    expires_at: Mapped[datetime] = Column(
        TIMESTAMP(timezone=True),
        default=(func.now() + func.cast(func.concat(14, "DAYS"), Interval)),
        server_default=(func.now() + func.cast(func.concat(14, "DAYS"), Interval)),
        nullable=False,
    )
    completed_at: Mapped[Optional[datetime]] = Column(TIMESTAMP(timezone=True))
    user_leave_administrator_action_type_id: Mapped[int] = Column(
        Integer,
        ForeignKey(LkUserLeaveAdministratorActionType.user_leave_administrator_action_type_id),
        nullable=False,
    )

    user: Mapped[User] = relationship(User)
    user_leave_administrator_action_type: Mapped[LkUserLeaveAdministratorActionType] = relationship(
        LkUserLeaveAdministratorActionType
    )
    recipient_employer: Mapped[Employer] = relationship(Employer)

    recipient_user_leave_administrator: Mapped[Optional[UserLeaveAdministrator]] = relationship(
        UserLeaveAdministrator, back_populates="user_leave_admin_actions"
    )

    @property
    def action_type(self):
        return self.user_leave_administrator_action_type


class LeaveAdminVerificationAttempt(Base, TimestampMixin):
    __tablename__ = "leave_admin_verification_attempt"
    leave_admin_verification_attempt_id = Column(
        SQL_UUID(as_uuid=True), primary_key=True, default=uuid_gen, nullable=False
    )
    employer_id = Column(
        SQL_UUID(as_uuid=True), ForeignKey("employer.employer_id"), nullable=False, index=True
    )
    user_id = Column(SQL_UUID(as_uuid=True), ForeignKey("user.user_id"), nullable=False, index=True)
    is_successful = Column(Boolean, nullable=False)

    employer: Mapped[Employer] = relationship(Employer)
    user: Mapped[User] = relationship(User)


class LkManagedRequirementStatus(Base):
    # Descriptions in this table map to Fineos Enum domain #239
    __tablename__ = "lk_managed_requirement_status"
    managed_requirement_status_id: Mapped[int] = Column(
        Integer, primary_key=True, autoincrement=True
    )
    managed_requirement_status_description: Mapped[str] = Column(Text, nullable=False)

    def __init__(self, managed_requirement_status_id, managed_requirement_status_description):
        self.managed_requirement_status_id = managed_requirement_status_id
        self.managed_requirement_status_description = managed_requirement_status_description


class LkManagedRequirementCategory(Base):
    __tablename__ = "lk_managed_requirement_category"
    managed_requirement_category_id: Mapped[int] = Column(
        Integer, primary_key=True, autoincrement=True
    )
    managed_requirement_category_description: Mapped[str] = Column(Text, nullable=False)

    def __init__(self, managed_requirement_category_id, managed_requirement_category_description):
        self.managed_requirement_category_id = managed_requirement_category_id
        self.managed_requirement_category_description = managed_requirement_category_description


class LkManagedRequirementType(Base):
    __tablename__ = "lk_managed_requirement_type"
    managed_requirement_type_id: Mapped[int] = Column(Integer, primary_key=True, autoincrement=True)
    managed_requirement_type_description: Mapped[str] = Column(Text, nullable=False)

    def __init__(self, managed_requirement_type_id, managed_requirement_type_description):
        self.managed_requirement_type_id = managed_requirement_type_id
        self.managed_requirement_type_description = managed_requirement_type_description


class ManagedRequirement(Base, TimestampMixin):
    """PFML-relevant data from a Managed Requirement in Fineos. Example managed requirement is an Employer info request."""

    __tablename__ = "managed_requirement"

    managed_requirement_id = Column(
        SQL_UUID(as_uuid=True),
        primary_key=True,
        default=uuid_gen,
        comment="Unique primary key id",
    )
    claim_id = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey("claim.claim_id"),
        index=True,
        nullable=False,
        comment="Internal id of claim associated with managed requirement",
    )
    respondent_user_id = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey("user.user_id"),
        comment="Internal id of user associated with managed requirement",
    )
    fineos_managed_requirement_id = Column(
        Text,
        unique=True,
        nullable=False,
        comment="Fineos id of managed requirement",
    )
    follow_up_date = Column(
        Date,
        comment="Follow up date associated with managed requirement",
    )
    responded_at = Column(
        TIMESTAMP(timezone=True),
        comment="Date of response to managed requirement",
    )
    managed_requirement_status_id = Column(
        Integer,
        ForeignKey("lk_managed_requirement_status.managed_requirement_status_id"),
        nullable=False,
        comment='id corresponding to an ManagedRequirementStatus enum, eg 1 ("Open")',
    )
    managed_requirement_category_id = Column(
        Integer,
        ForeignKey("lk_managed_requirement_category.managed_requirement_category_id"),
        nullable=False,
        comment='id corresponding to an ManagedRequirementCategory enum, eg 1 ("Employer Confirmation")',
    )
    managed_requirement_type_id = Column(
        Integer,
        ForeignKey("lk_managed_requirement_type.managed_requirement_type_id"),
        index=True,
        nullable=False,
        comment='id corresponding to an MangedRequirementType enum, eg 1 ("Employer Confirmation of Leave Data")',
    )

    managed_requirement_status: Mapped[LkManagedRequirementStatus] = relationship(
        LkManagedRequirementStatus
    )
    managed_requirement_category: Mapped[LkManagedRequirementCategory] = relationship(
        LkManagedRequirementCategory
    )
    managed_requirement_type: Mapped[LkManagedRequirementType] = relationship(
        LkManagedRequirementType
    )

    claim: Mapped["Claim"] = relationship("Claim", back_populates="managed_requirements")
    respondent_user: Mapped[Optional[User]] = relationship(User)

    @property
    def is_open(self):
        from massgov.pfml.db.lookup_data.employees import ManagedRequirementStatus

        return (
            self.managed_requirement_status_id
            == ManagedRequirementStatus.OPEN.managed_requirement_status_id
        )


class WagesAndContributions(Base, TimestampMixin):
    """Most up to date records we have about a given employee + employer + quarterly wages"""

    __tablename__ = "wages_and_contributions"

    wage_and_contribution_id: Mapped[UUID] = Column(
        SQL_UUID(as_uuid=True),
        primary_key=True,
        default=uuid_gen,
        comment="Unique primary key id",
    )
    account_key: Mapped[str] = Column(
        Text,
        nullable=False,
        comment="Account key of entity making contribution",
    )
    filing_period: Mapped[date] = Column(
        Date,
        nullable=False,
        index=True,
        comment="Quarterly filing period for which employee contributed",
    )
    employee_id: Mapped[UUID] = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey("employee.employee_id"),
        nullable=False,
        index=True,
        comment="Internal id for employee associated with contribution",
    )
    employer_id: Mapped[UUID] = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey("employer.employer_id"),
        nullable=False,
        index=True,
        comment="Internal id for employer associated with contribution",
    )
    is_independent_contractor = Column(
        Boolean,
        comment="Whether or not the employee is an independent ccontractor",
    )
    is_opted_in = Column(
        Boolean,
        comment="Whether or not employee is opted in",
    )
    employee_ytd_wages = Column(
        Numeric(asdecimal=True),
        comment="Year to date wages of employee associated with contribution",
    )
    employee_qtr_wages: Mapped[Decimal] = Column(
        Numeric(asdecimal=True),
        nullable=False,
        comment="Quarterly wage of employee associated with contribution",
    )
    employee_med_contribution = Column(
        Numeric(asdecimal=True),
        comment="Medical contribution of employee",
    )
    employer_med_contribution = Column(
        Numeric(asdecimal=True),
        comment="Medical contribution of employer",
    )
    employee_fam_contribution = Column(
        Numeric(asdecimal=True),
        comment="Family contribution of employee",
    )
    employer_fam_contribution = Column(
        Numeric(asdecimal=True),
        comment="Family contribution of employer",
    )
    latest_import_log_id = Column(
        Integer,
        ForeignKey("import_log.import_log_id"),
        index=True,
        comment="id value of latest import that modified this row",
    )
    wages_and_contributions_datasource_id = Column(
        Integer,
        ForeignKey("lk_wages_and_contributions_datasource.wages_and_contributions_datasource_id"),
        nullable=False,
        comment="ID corresponding to a member of the WagesAndContributionsDatasource enum",
    )

    datasource: Mapped["LkWagesAndContributionsDatasource"] = relationship(
        LkWagesAndContributionsDatasource
    )
    employee: Mapped["Employee"] = relationship(
        "Employee", back_populates="wages_and_contributions"
    )
    employer: Mapped["Employer"] = relationship(
        "Employer", back_populates="wages_and_contributions"
    )

    statutory_exclusion: Mapped[Optional[StatutorilyExcludedEmployer]] = relationship(
        primaryjoin=lambda: (Employer.employer_id == WagesAndContributions.employer_id),
        secondary=Employer.__table__,
        secondaryjoin=lambda: and_(
            StatutorilyExcludedEmployer.employer_fein == Employer.employer_fein,
            StatutorilyExcludedEmployer.is_current,
        ),
        viewonly=True,
        lazy="raise",  # do not load this from an instance, only via SQL
    )

    @hybrid_property
    def is_statutorily_excluded(self) -> bool:
        """
        Is this wage record associated with a statutorily excluded employer?
        Some employers are excluded from participating in PFML by statute. PFML
        applications against statutorily excluded employers are automatically denied.
        In addition to PFML claims for statutorily excluded employers being denied, any
        *wages* earned from those employers cannot be considered when computing an
        employee's financial eligibility for PFML against *any* employer -- even
        non-statutorily-excluded ones.
        """
        # (intentionally use != None so it works in both contexts of hybrid property)
        return self.statutory_exclusion != None  # noqa: E711

    @property
    def filing_quarter(self) -> Quarter:
        return Quarter.from_date(self.filing_period)


class WagesAndContributionsHistory(Base, TimestampMixin):
    """Archive of past employee + employer + quarterly wages records that have since been overwritten"""

    __tablename__ = "wages_and_contributions_history"

    wages_and_contributions_history_id = Column(
        SQL_UUID(as_uuid=True),
        primary_key=True,
        default=uuid_gen,
        comment="Unique primary key id",
    )
    is_independent_contractor = Column(
        Boolean,
        comment="Whether or not the employee is an independent ccontractor",
    )
    is_opted_in = Column(
        Boolean,
        comment="Whether or not employee is opted in",
    )
    employee_ytd_wages = Column(
        Numeric(asdecimal=True),
        comment="Year to date wages of employee associated with contribution",
    )
    employee_qtr_wages: Mapped[Decimal] = Column(
        Numeric(asdecimal=True),
        nullable=False,
        comment="Quarterly wage of employee associated with contribution",
    )
    employee_med_contribution = Column(
        Numeric(asdecimal=True),
        comment="Medical contribution of employee",
    )
    employer_med_contribution = Column(
        Numeric(asdecimal=True),
        comment="Medical contribution of employer",
    )
    employee_fam_contribution = Column(
        Numeric(asdecimal=True),
        comment="Family contribution of employee",
    )
    employer_fam_contribution = Column(
        Numeric(asdecimal=True),
        comment="Family contribution of employer",
    )
    import_log_id = Column(
        Integer,
        ForeignKey("import_log.import_log_id"),
        index=True,
        nullable=True,
        comment="id value of import that modified this row",
    )
    wage_and_contribution_id = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey("wages_and_contributions.wage_and_contribution_id"),
        index=True,
        nullable=False,
        comment="Internal id of wage and contribution",
    )
    wages_and_contributions_datasource_id = Column(
        Integer,
        ForeignKey("lk_wages_and_contributions_datasource.wages_and_contributions_datasource_id"),
        # TODO (PFMLPB-20540): Make required.
        nullable=True,
        comment="ID corresponding to a member of the WagesAndContributionsDatasource enum",
    )

    datasource: Mapped[Optional[LkWagesAndContributionsDatasource]] = relationship(
        LkWagesAndContributionsDatasource
    )
    wage_and_contribution: Mapped["WagesAndContributions"] = relationship("WagesAndContributions")


class WagesAndContributionsUnused(Base, TimestampMixin):
    """Records that would have been written but were considered to be less accurate due to policy choices"""

    __tablename__ = "wages_and_contributions_unused"

    wage_and_contribution_unused_id: Mapped[UUID] = Column(
        SQL_UUID(as_uuid=True),
        primary_key=True,
        default=uuid_gen,
        comment="Unique primary key id",
    )
    is_independent_contractor = Column(
        Boolean,
        comment="Whether or not the employee is an independent ccontractor",
    )
    is_opted_in = Column(
        Boolean,
        comment="Whether or not employee is opted in",
    )
    employee_ytd_wages = Column(
        Numeric(asdecimal=True),
        comment="Year to date wages of employee associated with contribution",
    )
    employee_qtr_wages: Mapped[Decimal] = Column(
        Numeric(asdecimal=True),
        nullable=False,
        comment="Quarterly wage of employee associated with contribution",
    )
    employee_med_contribution = Column(
        Numeric(asdecimal=True),
        comment="Medical contribution of employee",
    )
    employer_med_contribution = Column(
        Numeric(asdecimal=True),
        comment="Medical contribution of employer",
    )
    employee_fam_contribution = Column(
        Numeric(asdecimal=True),
        comment="Family contribution of employee",
    )
    employer_fam_contribution = Column(
        Numeric(asdecimal=True),
        comment="Family contribution of employer",
    )
    import_log_id = Column(
        Integer,
        ForeignKey("import_log.import_log_id"),
        index=True,
        nullable=True,
        comment="id value of import that modified this row",
    )
    wage_and_contribution_id = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey("wages_and_contributions.wage_and_contribution_id"),
        index=True,
        nullable=True,
        comment="Internal id of wage and contribution that this would have replaced",
    )
    wage_and_contribution_history_id = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey("wages_and_contributions_history.wages_and_contributions_history_id"),
        index=True,
        nullable=True,
        comment="Internal id of wage and contribution history that this would have replaced",
    )
    wages_and_contributions_datasource_id = Column(
        Integer,
        ForeignKey("lk_wages_and_contributions_datasource.wages_and_contributions_datasource_id"),
        # TODO (PFMLPB-20540): Make required.
        nullable=True,
        comment="ID corresponding to a member of the WagesAndContributionsDatasource enum",
    )

    datasource: Mapped[Optional[LkWagesAndContributionsDatasource]] = relationship(
        LkWagesAndContributionsDatasource
    )
    wage_and_contribution: Mapped["WagesAndContributions"] = relationship("WagesAndContributions")
    wage_and_contribution_history: Mapped["WagesAndContributionsHistory"] = relationship(
        "WagesAndContributionsHistory"
    )


class EmployeeOccupation(Base, TimestampMixin):
    """The occupation of an employee"""

    __tablename__ = "employee_occupation"

    employee_occupation_id = Column(
        SQL_UUID(as_uuid=True),
        primary_key=True,
        default=uuid_gen,
        comment="Unique primary key id",
    )
    employee_id: Mapped[UUID] = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey("employee.employee_id"),
        nullable=False,
        index=True,
        comment="Internal id of employee associated with employee occupation",
    )
    employer_id: Mapped[UUID] = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey("employer.employer_id"),
        nullable=False,
        index=True,
        comment="Internal id of employer associated with employee occupaton",
    )
    organization_unit_id: Mapped[Optional[UUID]] = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey("organization_unit.organization_unit_id"),
        nullable=True,
        index=True,
        comment="Internal id of organization unit associated with employee occupation",
    )
    job_title = Column(
        Text,
        comment="Job Title of employee associated with this employee occupation",
    )
    date_of_hire = Column(
        Date,
        comment="Date of hire of employee associated with this employee occupation",
    )
    date_job_ended = Column(
        Date,
        comment="Date job ended of employee associated with this employee occupation",
    )
    employment_status = Column(
        Text,
        comment="Employee status of employee associated with this employee occupation",
    )
    hours_worked_per_week = Column(
        Numeric,
        comment="Hours worked per week of employee associated with this employee occupation",
    )
    days_worked_per_week = Column(
        Numeric,
        comment="Days worked per week of employee associated with this employee occupation",
    )
    manager_id = Column(
        Text,
        comment="id of manager of employee associated with this employee occupation",
    )
    worksite_id = Column(
        Text,
        comment="id of worksite of employee associated with this employee occupation",
    )
    occupation_qualifier = Column(
        Text,
        comment="Occupation qualifier of employee associated with this employee occupation",
    )

    Index("ix_employee_occupation_employee_id_employer_id", employee_id, employer_id, unique=True)

    employee: Mapped["Employee"] = relationship("Employee", back_populates="employee_occupations")
    employer: Mapped["Employer"] = relationship("Employer", back_populates="employer_occupations")
    organization_unit: Mapped[Optional["OrganizationUnit"]] = relationship("OrganizationUnit")


class Overpayment(Base, TimestampMixin):
    __tablename__ = "overpayment"
    overpayment_id = Column(SQL_UUID(as_uuid=True), primary_key=True, default=uuid_gen)

    claim_id = Column(
        SQL_UUID(as_uuid=True), ForeignKey("claim.claim_id"), index=True, nullable=True
    )
    claim: Mapped[Optional[Claim]] = relationship(Claim)

    period_start_date = Column(Date, nullable=True)
    period_end_date = Column(Date, nullable=True)
    overpayment_date = Column(Date, nullable=True)
    amount: Mapped[Optional[Decimal]] = Column(Numeric(asdecimal=True), nullable=True)
    fineos_pei_c_value = Column(Text, nullable=True)
    fineos_pei_i_value = Column(Text, nullable=True)

    claim_type_id = Column(Integer, ForeignKey("lk_claim_type.claim_type_id"))
    claim_type: Mapped[Optional[LkClaimType]] = relationship(LkClaimType)

    payment_event_type_id = Column(
        Integer,
        ForeignKey("lk_payment_event_type.payment_event_type_id"),
        index=True,
        nullable=True,
    )

    absence_case_creation_date = Column(Date, nullable=True)

    leave_request_id = Column(
        SQL_UUID(as_uuid=True), ForeignKey("absence_period.absence_period_id")
    )
    leave_request: Mapped[Optional["AbsencePeriod"]] = relationship("AbsencePeriod")

    fineos_employee_first_name = Column(Text, nullable=True)
    fineos_employee_last_name = Column(Text, nullable=True)
    fineos_employee_middle_name = Column(Text, nullable=True)

    employee_id: Mapped[Optional[UUID]] = Column(
        SQL_UUID(as_uuid=True), ForeignKey("employee.employee_id"), index=True, nullable=True
    )
    employee: Mapped[Optional[Employee]] = relationship(Employee)

    vpei_id = Column(
        SQL_UUID(as_uuid=True), ForeignKey("fineos_extract_vpei.vpei_id"), nullable=True
    )

    fineos_leave_request_id = Column(Integer, nullable=True)
    fineos_extraction_date = Column(Date, nullable=True)

    fineos_extract_import_log_id = Column(
        Integer, ForeignKey("import_log.import_log_id"), index=True
    )
    fineos_extract_import_log: Mapped[Optional["ImportLog"]] = relationship("ImportLog")

    payment_relevant_party_id = Column(
        Integer, ForeignKey("lk_payment_relevant_party.payment_relevant_party_id")
    )
    payment_relevant_party: Mapped[Optional[LkPaymentRelevantParty]] = relationship(
        LkPaymentRelevantParty
    )

    payment_transaction_type_id = Column(
        Integer, ForeignKey("lk_payment_transaction_type.payment_transaction_type_id")
    )
    payment_transaction_type: Mapped[Optional[LkPaymentTransactionType]] = relationship(
        LkPaymentTransactionType
    )
    overpayment_details = cast(
        List["OverpaymentDetails"],
        relationship(
            "OverpaymentDetails",
            back_populates="overpayment",
            order_by="OverpaymentDetails.period_start_date",
        ),
    )
    overpayment_casenumber = Column(Text, index=True, nullable=True)

    net_benefit_overpayment_amount = Column(Numeric(asdecimal=True), nullable=True)

    overpayment_adjustment_amount = Column(Numeric(asdecimal=True), nullable=True)

    agreed_recovery_amount = Column(Numeric(asdecimal=True), nullable=True)

    recovered_to_date_amount = Column(Numeric(asdecimal=True), nullable=True)

    outstanding_amount = Column(Numeric(asdecimal=True), nullable=True)

    adjustment_name = Column(Text, nullable=True)

    adjustment_amount = Column(Numeric(asdecimal=True), nullable=True)

    adjustment_agreement_date = Column(Date, nullable=True)

    adjustment_description = Column(Text, nullable=True)

    overpayment_doc_id_seq: Sequence = Sequence(
        "overpayment_doc_id_seq", data_type=BIGINT, metadata=Base.metadata
    )

    ctr_doc_id = Column(
        Text, nullable=True, comment="DOC_ID assigned to the RE records sent to MMARS"
    )

    ctr_doc_version_number = Column(Integer, nullable=False, default=1, server_default="1")

    # Function to get or set the unique ID
    @property
    def get_or_generate_unique_id(self) -> str:

        if self.ctr_doc_id is not None:
            return self.ctr_doc_id
        else:
            # If the doc_id is not set, generate a new one
            # The format is "INTFRE" + 14-digit sequence number
            db_session = get_object_session(self)

            if not db_session:
                raise Exception("No session found")

            next_val = db_session.execute(self.overpayment_doc_id_seq.next_value()).scalar()

            if next_val is None or not isinstance(next_val, int):
                raise ValueError("Expected a string result, but got None or a non-string value")

            padded_val = str(next_val).zfill(14)
            self.ctr_doc_id = f"{PREFIX_INTF}{PREFIX_RE}{padded_val}"

            return self.ctr_doc_id


class OverpaymentDetails(Base, TimestampMixin):
    __tablename__ = "overpayment_details"
    overpayment_details_id = Column(SQL_UUID(as_uuid=True), primary_key=True, default=uuid_gen)
    overpayment_id = Column(
        SQL_UUID(as_uuid=True), ForeignKey(Overpayment.overpayment_id), index=True, nullable=False
    )
    period_start_date = Column(Date)
    period_end_date = Column(Date)
    amount = Column(Numeric(asdecimal=True), nullable=False)
    business_net_amount = Column(Numeric(asdecimal=True))
    overpayment_details_c_value = Column(Text)
    overpayment_details_i_value = Column(Text)

    fineos_extraction_date = Column(Date, nullable=True)

    overpayment: Mapped[Overpayment] = relationship(Overpayment)


class OverpaymentRepayment(Base, TimestampMixin):
    __tablename__ = "overpayment_repayment"
    overpayment_repayment_id = Column(SQL_UUID(as_uuid=True), primary_key=True, default=uuid_gen)
    cancelled_overpayment_repayment_id = Column(SQL_UUID(as_uuid=True), nullable=True)
    overpayment_repayment_date = Column(Date, nullable=True)
    amount: Mapped[Optional[Decimal]] = Column(Numeric(asdecimal=True), nullable=True)
    fineos_pei_c_value = Column(Text, nullable=True)
    fineos_pei_i_value = Column(Text, nullable=True)
    payment_event_type_id = Column(
        Integer,
        ForeignKey("lk_payment_event_type.payment_event_type_id"),
        index=True,
        nullable=True,
    )
    overpayment_recovery_type_id = Column(
        Integer,
        ForeignKey("lk_overpayment_recovery_type.overpayment_recovery_type_id"),
        index=True,
        nullable=True,
    )

    fineos_employee_first_name = Column(Text, nullable=True)
    fineos_employee_last_name = Column(Text, nullable=True)
    fineos_employee_middle_name = Column(Text, nullable=True)
    fineos_extraction_date = Column(Date, nullable=True)

    employee_id: Mapped[Optional[UUID]] = Column(
        SQL_UUID(as_uuid=True), ForeignKey("employee.employee_id"), index=True, nullable=True
    )
    employee = cast(Optional[Employee], relationship(Employee))

    vpei_id = Column(SQL_UUID(as_uuid=True), ForeignKey("fineos_extract_vpei.vpei_id"))

    fineos_extract_import_log_id = Column(
        Integer, ForeignKey("import_log.import_log_id"), index=True
    )
    fineos_extract_import_log: Mapped[Optional["ImportLog"]] = relationship("ImportLog")


class OverpaymentAdjustment(Base, TimestampMixin):
    __tablename__ = "overpayment_adjustment"

    __table_args__ = (
        UniqueConstraint(  # these three columns should be unique together
            "overpaymentcase_overpayment_id",
            "adjustment_agreement_date",
            "adjustment_amount",
            name="uq_overpayment_adjustment",
        ),
    )

    overpayment_adjustment_id = Column(SQL_UUID(as_uuid=True), primary_key=True, default=uuid_gen)
    overpaymentcase_overpayment_id: Mapped[Optional[UUID]] = Column(
        SQL_UUID(as_uuid=True), ForeignKey("overpayment.overpayment_id"), index=True, nullable=False
    )
    overpaymentcase_overpayment: Mapped[Overpayment] = relationship(Overpayment)
    adjustment_amount = Column(Numeric(asdecimal=True), nullable=False)
    adjustment_agreement_date = Column(Date, nullable=False)
    payment_status = deprecated_column(Text, nullable=True)
    adjustment_description = Column(Text, nullable=True)
    overpayment_adjustment_payment_status_id = Column(
        Integer,
        ForeignKey(
            "lk_overpayment_adjustment_payment_status.overpayment_adjustment_payment_status_id"
        ),
    )


class StateLog(Base, TimestampIndexMixin):
    __tablename__ = "state_log"
    state_log_id: Mapped[UUID] = Column(SQL_UUID(as_uuid=True), primary_key=True, default=uuid_gen)
    end_state_id: Mapped[Optional[int]] = Column(
        Integer, ForeignKey("lk_state.state_id"), index=True
    )
    started_at = Column(TIMESTAMP(timezone=True))
    ended_at = Column(TIMESTAMP(timezone=True), index=True)
    outcome = Column(JSON)
    payment_id: Mapped[Optional[UUID]] = Column(
        SQL_UUID(as_uuid=True), ForeignKey("payment.payment_id"), index=True
    )
    reference_file_id: Mapped[Optional[UUID]] = Column(
        SQL_UUID(as_uuid=True), ForeignKey("reference_file.reference_file_id"), index=True
    )
    employee_id: Mapped[Optional[UUID]] = Column(
        SQL_UUID(as_uuid=True), ForeignKey("employee.employee_id"), index=True
    )
    claim_id: Mapped[Optional[UUID]] = Column(
        SQL_UUID(as_uuid=True), ForeignKey("claim.claim_id"), index=True
    )
    prev_state_log_id: Mapped[Optional[UUID]] = Column(
        SQL_UUID(as_uuid=True), ForeignKey("state_log.state_log_id")
    )
    associated_type = Column(Text, index=True)

    import_log_id = Column(
        Integer, ForeignKey("import_log.import_log_id"), index=True, nullable=True
    )

    end_state: Mapped[Optional["LkState"]] = relationship(LkState, foreign_keys=[end_state_id])
    payment: Mapped[Optional["Payment"]] = relationship("Payment", back_populates="state_logs")
    reference_file: Mapped[Optional[ReferenceFile]] = relationship(
        ReferenceFile, backref="state_logs"
    )
    employee: Mapped[Optional["Employee"]] = relationship("Employee", back_populates="state_logs")
    claim: Mapped[Optional["Claim"]] = relationship("Claim", back_populates="state_logs")
    prev_state_log: Mapped[Optional["StateLog"]] = relationship(
        "StateLog", uselist=False, remote_side=state_log_id
    )
    import_log: Mapped[Optional[ImportLog]] = relationship(ImportLog, foreign_keys=[import_log_id])


class LatestStateLog(Base, TimestampMixin):
    __tablename__ = "latest_state_log"
    latest_state_log_id: Mapped[UUID] = Column(
        SQL_UUID(as_uuid=True), primary_key=True, default=uuid_gen
    )

    state_log_id: Mapped[UUID] = Column(
        SQL_UUID(as_uuid=True), ForeignKey("state_log.state_log_id"), index=True, nullable=False
    )
    payment_id: Mapped[Optional[UUID]] = Column(
        SQL_UUID(as_uuid=True), ForeignKey("payment.payment_id"), index=True
    )
    employee_id: Mapped[Optional[UUID]] = Column(
        SQL_UUID(as_uuid=True), ForeignKey("employee.employee_id"), index=True
    )
    claim_id: Mapped[Optional[UUID]] = Column(
        SQL_UUID(as_uuid=True), ForeignKey("claim.claim_id"), index=True
    )
    reference_file_id: Mapped[Optional[UUID]] = Column(
        SQL_UUID(as_uuid=True), ForeignKey("reference_file.reference_file_id"), index=True
    )

    state_log: Mapped[Optional["StateLog"]] = relationship("StateLog")
    payment: Mapped[Optional["Payment"]] = relationship("Payment")
    employee: Mapped[Optional["Employee"]] = relationship("Employee")
    claim: Mapped[Optional["Claim"]] = relationship("Claim")
    reference_file: Mapped[Optional[ReferenceFile]] = relationship(ReferenceFile)


class DuaReductionPayment(Base, TimestampMixin):
    """A payment made by the MA Department of Unemployment Assistance. May contain PII."""

    __tablename__ = "dua_reduction_payment"

    __extra_table_args__ = (
        # Each row should be unique. This enables us to load only new rows from
        # a CSV and ensures that we don't include payments twice as two
        # different rows. Almost all fields are nullable so we have to coalesce
        # those null values to empty strings.
        Index(
            "dua_reduction_payment_unique_payment_data_idx",
            "fineos_customer_number",
            text("coalesce(employer_fein, '')"),
            text("coalesce(payment_date, '1788-02-06')"),
            text("coalesce(request_week_begin_date, '1788-02-06')"),
            text("coalesce(gross_payment_amount_cents, 99999999)"),
            text("coalesce(payment_amount_cents, 99999999)"),
            text("coalesce(fraud_indicator, '')"),
            text("coalesce(benefit_year_end_date, '1788-02-06')"),
            text("coalesce(benefit_year_begin_date, '1788-02-06')"),
            unique=True,
        ),
    )

    dua_reduction_payment_id = Column(SQL_UUID(as_uuid=True), primary_key=True, default=uuid_gen)

    fineos_customer_number = Column(Text, nullable=False)
    employer_fein = Column(Text)
    payment_date = Column(Date)
    request_week_begin_date = Column(Date)
    gross_payment_amount_cents = Column(Integer)
    payment_amount_cents = Column(Integer)
    fraud_indicator = Column(Text)
    benefit_year_begin_date = Column(Date)
    benefit_year_end_date = Column(Date)

    # Each row should be unique. This enables us to load only new rows from a CSV and ensures that
    # we don't include payments twice as two different rows. Almost all fields are nullable so we
    # have to coalesce those null values to empty strings. We've manually adjusted the migration
    # that adds this unique constraint to coalesce those nullable fields.
    # See: 2021_01_29_15_51_16_14155f78d8e6_create_dua_reduction_payment_table.py


class DiaReductionPayment(Base, TimestampMixin):
    """A payment made by the MA Department of Industrial Accidents. May contain PII."""

    __tablename__ = "dia_reduction_payment"

    __extra_table_args__ = (
        # Each row should be unique.
        Index(
            "dia_reduction_payment_unique_payment_data_idx",
            "fineos_customer_number",
            text("coalesce(board_no, '')"),
            text("coalesce(event_id, '')"),
            text("coalesce(event_description, '')"),
            text("coalesce(eve_created_date, '1788-02-06')"),
            text("coalesce(event_occurrence_date, '1788-02-06')"),
            text("coalesce(award_id, '')"),
            text("coalesce(award_code, '')"),
            text("coalesce(award_amount, 99999999)"),
            text("coalesce(award_date, '1788-02-06')"),
            text("coalesce(start_date, '1788-02-06')"),
            text("coalesce(end_date, '1788-02-06')"),
            text("coalesce(weekly_amount, 99999999)"),
            text("coalesce(award_created_date, '1788-02-06')"),
            text("coalesce(termination_date, '1788-02-06')"),
            unique=True,
        ),
    )

    dia_reduction_payment_id = Column(SQL_UUID(as_uuid=True), primary_key=True, default=uuid_gen)

    fineos_customer_number: Mapped[str] = Column(Text, nullable=False)
    board_no = Column(Text)
    event_id = Column(Text)
    event_description = Column(Text)
    eve_created_date = Column(Date)
    event_occurrence_date = Column(Date)
    award_id = Column(Text)
    award_code = Column(Text)
    award_amount: Mapped[Decimal | None] = Column(Numeric(asdecimal=True))
    award_date = Column(Date)
    start_date = Column(Date)
    end_date = Column(Date)
    weekly_amount: Mapped[Decimal | None] = Column(Numeric(asdecimal=True))
    award_created_date = Column(Date)
    termination_date = Column(Date)

    # Each row should be unique.

    Index(
        "ix_dia_reduction_payment_fineos_customer_number_board_no",
        fineos_customer_number,
        board_no,
        unique=False,
    )
