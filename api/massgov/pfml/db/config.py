import os
from typing import Optional

from pydantic import Field, PositiveInt, SecretStr, ValidationError

import massgov.pfml.util.logging
from massgov.pfml.util.pydantic import PydanticBaseSettings

logger = massgov.pfml.util.logging.get_logger(__name__)

NOT_SET = "ENV_VAR_NOT_SET"


class DbConfig(PydanticBaseSettings):
    host: str = "localhost"
    # defining the alias for backwards compatibility with some logging
    name: str = Field("pfml", alias="dbname", env="DB_NAME")
    username: str = "pfml_api"
    password: Optional[SecretStr] = None
    # A plain `schema` attribute conflicts with builtin pydantic stuff, so until
    # we use pydantic v2, do this
    schema_name: str = Field("public", alias="schema", env="DB_SCHEMA")
    port: PositiveInt = 5432
    use_iam_auth: bool = False
    hide_sql_parameter_logs: bool = True
    echo_statements: bool = False
    statement_timeout: int = 3600000
    migrate_post_connect_role: Optional[str] = None
    """How long Postgres will run a SQL statement before erroring out with a timeout exception

     This prevents exceedingly long queries from running forever.

     The default here is set to 60 minutes and is specified in MS 1000 * 60 * 60
    """

    class Config:
        allow_population_by_field_name = True
        env_prefix = "DB_"


class DbAdminConfig(DbConfig):
    username: str = Field(..., env="DB_ADMIN_USERNAME")
    password: Optional[SecretStr] = Field(None, env="DB_ADMIN_PASSWORD")


def get_config(prefer_admin: bool = False) -> DbConfig:
    db_config: DbConfig
    if prefer_admin:
        try:
            db_config = DbAdminConfig()
        except ValidationError:
            db_config = DbConfig()
    else:
        db_config = DbConfig()

    if os.getenv("ENVIRONMENT") != "local" and db_config.password is None:
        db_config.use_iam_auth = True

    logger.info("Constructed database configuration", extra=db_config.dict(by_alias=True))

    return db_config


class DbMigrationS3Config(PydanticBaseSettings):
    pfml_schema_changes_directory: str = Field(
        NOT_SET, description="Directory that PFML schema changes will be written to."
    )
    enable_notify_edm_team_of_pfml_schema_changes: bool = Field(
        False, description="Enable for notifying the EDM team of PFML schema changes."
    )


def get_s3_config():
    return DbMigrationS3Config()


class DbMigrationEmailConfig(PydanticBaseSettings):
    bounce_forwarding_email_address_arn: str
    bounce_forwarding_email_address: str
    pfml_email_address: str
    dfml_db_migration_email_address: str
    dfml_db_migration_cc_email_address: str


def get_email_config():
    db_migration_email_config = DbMigrationEmailConfig()

    logger.info(
        "Constructed DB migration email configuration",
        extra={
            "pfml_email_address": db_migration_email_config.pfml_email_address,
            "bounce_forwarding_email_address": db_migration_email_config.bounce_forwarding_email_address,
            "bounce_forwarding_email_address_arn": db_migration_email_config.bounce_forwarding_email_address_arn,
            "dfml_db_migration_email_address": db_migration_email_config.dfml_db_migration_email_address,
            "dfml_db_migration_cc_email_address": db_migration_email_config.dfml_db_migration_cc_email_address,
        },
    )

    return db_migration_email_config
