from massgov.pfml.db.lookup import LookupTable
from massgov.pfml.db.models.documents import LkDocumentType


class DocumentType(LookupTable):
    model = LkDocumentType
    column_names = ("document_type_id", "document_type_description")

    PASSPORT = LkDocumentType(1, "Passport")
    DRIVERS_LICENSE_MASS = LkDocumentType(2, "Driver's License Mass")
    DRIVERS_LICENSE_OTHER_STATE = LkDocumentType(3, "Driver's License Other State")
    IDENTIFICATION_PROOF = LkDocumentType(4, "Identification Proof")
    STATE_MANAGED_PAID_LEAVE_CONFIRMATION = LkDocumentType(
        5, "State managed Paid Leave Confirmation"
    )
    APPROVAL_NOTICE = LkDocumentType(6, "Approval Notice")
    REQUEST_FOR_MORE_INFORMATION = LkDocumentType(7, "Request for more Information")
    DENIAL_NOTICE = LkDocumentType(8, "Denial Notice")
    OWN_SERIOUS_HEALTH_CONDITION_FORM = LkDocumentType(9, "Own serious health condition form")
    PREGNANCY_MATERNITY_FORM = LkDocumentType(10, "Pregnancy/Maternity form")
    CHILD_BONDING_EVIDENCE_FORM = LkDocumentType(11, "Child bonding evidence form")
    CARE_FOR_A_FAMILY_MEMBER_FORM = LkDocumentType(12, "Care for a family member form")
    MILITARY_EXIGENCY_FORM = LkDocumentType(13, "Military exigency form")
    WITHDRAWAL_NOTICE = LkDocumentType(14, "Pending Application Withdrawn")
    APPEAL_ACKNOWLEDGMENT = LkDocumentType(15, "Appeal Acknowledgment")
    MAXIMUM_WEEKLY_BENEFIT_CHANGE_NOTICE = LkDocumentType(
        16, "Maximum Weekly Benefit Change Notice"
    )
    BENEFIT_AMOUNT_CHANGE_NOTICE = LkDocumentType(17, "Benefit Amount Change Notice")
    LEAVE_ALLOTMENT_CHANGE_NOTICE = LkDocumentType(18, "Leave Allotment Change Notice")
    APPROVED_TIME_CANCELLED = LkDocumentType(19, "Approved Time Cancelled")
    CHANGE_REQUEST_APPROVED = LkDocumentType(20, "Change Request Approved")
    CHANGE_REQUEST_DENIED = LkDocumentType(21, "Change Request Denied")
    IRS_1099G_TAX_FORM_FOR_CLAIMANTS = LkDocumentType(22, "1099G Tax Form for Claimants")
    APPEAL_FORM = LkDocumentType(23, "Appeal Form")
    APPEALS_SUPPORTING_DOCUMENTATION = LkDocumentType(24, "Appeals Supporting Documentation")
    USER_NOT_FOUND_INFO = LkDocumentType(25, "Employee Not Found Information")
    COVERED_SERVICE_MEMBER_ID_PROOF = LkDocumentType(
        26, "Covered Service Member Identification Proof"
    )
    FAMILY_MEMBER_ACTIVE_DUTY_SERVICE_PROOF = LkDocumentType(
        27, "Family Member Active Duty Service Proof"
    )
    OVERPAYMENT_NOTICE_PARTIAL_BALANCE_RECOVERY = LkDocumentType(
        28, "OP- Partial Recovery and Remaining Bal"
    )
    OVERPAYMENT_PAYOFF_NOTICE = LkDocumentType(29, "Overpayment Payoff Notice")
    OVERPAYMENT_NOTICE_FULL_BALANCE_RECOVERY = LkDocumentType(30, "OP- Full Balance Recovery")
    OVERPAYMENT_NOTICE_FULL_BALANCE_RECOVERY_MANUAL = LkDocumentType(
        31, "OP- Full Balance Recovery - Manual"
    )
    OVERPAYMENT_NOTICE_FULL_BALANCE_DEMAND = LkDocumentType(
        32, "Overpayment Notice-Full Balance Demand"
    )
    APPEAL_HEARING_VIRTUAL_FILLABLE = LkDocumentType(33, "Appeal Hearing Virtual Fillable")
    APPEAL_RFI = LkDocumentType(34, "Appeal RFI")
    APPEAL_RETURNED_TO_ADJUDICATION = LkDocumentType(35, "Appeal - Returned to Adjudication")
    APPEAL_APPROVED = LkDocumentType(36, "Appeal Approved")
    APPEAL_DISMISSED_OTHER = LkDocumentType(37, "Appeal Dismissed - Other")
    APPEAL_DISMISSED_EXEMPT_EMPLOYER = LkDocumentType(38, "Appeal Dismissed - Exempt Employer")
    MODIFY_DECISION = LkDocumentType(39, "Modify Decision")
    APPEAL_WITHDRAWN = LkDocumentType(40, "Appeal Withdrawn")
    INTERMITTENT_TIME_APPROVED_NOTICE = LkDocumentType(41, "Intermittent Time Approved Notice")
    PAYMENT_RECEIVED_UPDATED_OVERPAYMENT_BALANCE = LkDocumentType(
        42, "Payment Received-Updated Overpayment Balance"
    )
    DENIAL_NOTICE_EXPLANATION_OF_WAGES = LkDocumentType(43, "Denial Notice Explanation of Wages")
    EXPLANATION_OF_WAGES = LkDocumentType(44, "Explanation of Wages")
    POST_ADJUDICATION_REPORT = LkDocumentType(45, "Post-Adjudication Report")
    HEALTHCARE_PROVIDER_FORM = LkDocumentType(46, "Healthcare Provider Form")
    APPROVAL_OF_APPLICATION_CHANGE = LkDocumentType(
        47, "Approval of Application Change"
    )  # updated CHANGE_REQUEST_APPROVED
    DENIAL_OF_APPLICATION_CHANGE = LkDocumentType(
        48, "Denial of Application Change"
    )  # updated CHANGE REQUEST DENIED
    APPROVED_LEAVE_DATES_CANCELLED = LkDocumentType(
        49, "Approved Leave Dates Cancelled"
    )  # updated APPROVED_TIME_CANCELLED
    DENIAL_OF_APPLICATION = LkDocumentType(50, "Denial of Application")  # updated DENIAL_NOTICE
    INTERMITTENT_TIME_REPORTED = LkDocumentType(
        51, "Intermittent Time Reported"
    )  # updated INTERMITTENT_TIME_APPROVED_NOTICE
    APPROVAL_NOTICE_EXPLANATION_OF_WAGES = LkDocumentType(
        52, "Approval Notice Explanation of Wages"
    )
    OVERPAYMENT_FULL_DEMAND_ER_BENEFITS = LkDocumentType(53, "Overpayment Full Demand ER Benefits")
    OVERPAYMENT_FULL_DEMAND_INTERMITTENT = LkDocumentType(
        54, "Overpayment Full Demand Intermittent"
    )
    OVERPAYMENT_FULL_DEMAND_LEAVE_CHANGE = LkDocumentType(
        55, "Overpayment Full Demand Leave Change"
    )
    OVERPAYMENT_FULL_DEMAND_PAID_TIME_OFF = LkDocumentType(
        56, "Overpayment Full Demand Paid Time Off"
    )
    OVERPAYMENT_FULL_DEMAND_UI = LkDocumentType(57, "Overpayment Full Demand UI")
    OVERPAYMENT_FULL_DEMAND_WORKERS_COMP = LkDocumentType(
        58, "Overpayment Full Demand Workers Comp"
    )
    OVERPAYMENT_FULL_RECOVERY_ER_BENEFITS = LkDocumentType(
        59, "Overpayment Full Recovery ER Benefits"
    )
    OVERPAYMENT_FULL_RECOVERY_INTERMITTENT = LkDocumentType(
        60, "Overpayment Full Recovery Intermittent"
    )
    OVERPAYMENT_FULL_RECOVERY_LEAVE_CHANGE = LkDocumentType(
        61, "Overpayment Full Recovery Leave Change"
    )
    OVERPAYMENT_FULL_RECOVERY_PAID_TIME_OFF = LkDocumentType(
        62, "Overpayment Full Recovery Paid Time Off"
    )
    OVERPAYMENT_FULL_RECOVERY_UI = LkDocumentType(63, "Overpayment Full Recovery UI")
    OVERPAYMENT_FULL_RECOVERY_WORKERS_COMP = LkDocumentType(
        64, "Overpayment Full Recovery Workers Comp"
    )
    OVERPAYMENT_PARTIAL_DEMAND_ER_BENEFITS = LkDocumentType(
        65, "Overpayment Partial Demand ER Benefits"
    )
    OVERPAYMENT_PARTIAL_DEMAND_INTERMITTENT = LkDocumentType(
        66, "Overpayment Partial Demand Intermittent"
    )
    OVERPAYMENT_PARTIAL_LEAVE_CHANGE = LkDocumentType(67, "Overpayment Partial Leave Change")
    OVERPAYMENT_PARTIAL_PAID_TIME_OFF = LkDocumentType(68, "Overpayment Partial Paid Time Off")
    OVERPAYMENT_PARTIAL_DEMAND_UI = LkDocumentType(69, "Overpayment Partial Demand UI")
    OVERPAYMENT_PARTIAL_DEMAND_WORKERS_COMP = LkDocumentType(
        70, "Overpayment Partial Demand Workers Comp"
    )
    OVERPAYMENT_PAYMENT_RECEIVED_NEW_BALANCE = LkDocumentType(
        71, "Overpayment Payment Received New Balance"
    )
    OVERPAYMENT_PAYOFF = LkDocumentType(72, "Overpayment Payoff")
    DISMISSAL_FOR_FAILURE_TO_ATTEND_HEARING = LkDocumentType(
        73, "Dismissal for Failure to Attend Hearing"
    )
    W9_TAX_FORM = LkDocumentType(74, "W9 Tax Form")
    EFT_CHANGE_REQUEST = LkDocumentType(75, "EFT Change Request")
    NOTICE_OF_CHILD_SUPPORT_WITHHOLDING = LkDocumentType(76, "Notice of Child Support Withholding")
    APPEAL_POSTPONEMENT_AGENCY = LkDocumentType(77, "Appeal Postponement Agency")
    APPEAL_POSTPONEMENT_APPROVED = LkDocumentType(78, "Appeal Postponement Approved")
    APPEAL_POSTPONEMENT_DENIED = LkDocumentType(79, "Appeal Postponement Denied")
    APPEAL_REINSTATEMENT_DENIED = LkDocumentType(80, "Appeal Reinstatement Denied")
    APPEAL_REINSTATEMENT_GRANTED = LkDocumentType(81, "Appeal Reinstatement Granted")
    NOTICE_OF_DEFAULT = LkDocumentType(82, "Notice of Default")
    CONFIRMATION_OF_INSURANCE_FORM = LkDocumentType(84, "Confirmation of Insurance Form")
    SELF_INSURANCE_DECLARATION_DOCUMENT = LkDocumentType(85, "Self-Insurance Declaration Document")
    SELF_INSURANCE_SURETY_BOND = LkDocumentType(86, "Self-Insurance Surety Bond")
    SELF_INSURANCE_PROOF_OF_BENEFITS = LkDocumentType(87, "Self-Insurance Proof of Benefits")
    FMLA = LkDocumentType(88, "Family and Medical Leave Act Form")
    MMG_IDV_MOCK_ID = LkDocumentType(89, "Mock ID Proof")
